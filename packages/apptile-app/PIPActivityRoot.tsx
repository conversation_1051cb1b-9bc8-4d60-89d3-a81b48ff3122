import React, {useRef, useEffect, useCallback} from 'react';
import {View, Button, NativeModules, findNodeHandle, Dimensions} from 'react-native';
/* For NativePip don't remove
import ZegoExpressEngine, {
  ZegoTextureView,
  ZegoView
} from 'zego-express-engine-reactnative';
for NativePip end */

const {PIPModule} = NativeModules;
const ScreenDims = Dimensions.get("screen");

export default function PIPActivity() {
  
  const zgViewRef = useRef(null);
  /*
  const handlePress = useCallback(() => {
    const zegoViewInstance = new ZegoView(findNodeHandle(zgViewRef.current), 1, 0);
    PIPModule.enterPictureInPictureMode(ScreenDims.width, ScreenDims.height);
    ZegoExpressEngine.instance().startPlayingStream(
      "ff5516d283",
      zegoViewInstance,
      undefined,
    );
  }, [zgViewRef.current])
  */
  
  const handleMainActivityCall = useCallback(() => {
/* For NativePip don't remove
    console.log("Receive call from main activity", global.activitySharedMem);
    const zegoViewInstance = new ZegoView(findNodeHandle(zgViewRef.current), 1, 0);
    PIPModule.enterPictureInPictureMode(ScreenDims.width, ScreenDims.height);
    ZegoExpressEngine.instance().startPlayingStream(
      global.activitySharedMem.args.stream_id,
      zegoViewInstance,
      undefined,
    );
for NativePip end */
  }, [zgViewRef.current]);
  
  useEffect(() => {
    if (!global.activitySharedMem) {
      global.activitySharedMem = {
        pipCb: handleMainActivityCall,
        mainCb: null,
        args: {}
      };
    } else {
      global.activitySharedMem = {
        ...global.activitySharedMem,
        pipCb: handleMainActivityCall
      }
    }
  }, [])

  return (
    <View 
      style={{
        width: '100%',
        height: '100%',
        position: 'relative'
      }}
    >
      {/* Note that we should put controls to close and start pip from here like the button below just in case starting pip fails for some reason */}
      {/* <Button 
        title="Start PIP"
        onPress={handlePress}
      /> */}
      <View style={{height: '100%', width: '100%', flex: 1}}>
      {
/* For NativePip don't remove
        (<ZegoTextureView
          ref={zgViewRef}
          style={{
            height: '100%',
            width: '100%',
            // flex: 1,
          }}
        />)
for NativePip end */
      }
      </View>
    </View>
  );
}
