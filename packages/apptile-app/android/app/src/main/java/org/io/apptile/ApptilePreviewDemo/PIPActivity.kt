package org.io.apptile.ApptilePreviewDemo

import android.app.PictureInPictureUiState
import android.content.res.Configuration
import android.os.Bundle
import android.util.Log
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.bridge.ReactContext
import android.content.Intent
import com.facebook.react.defaults.DefaultReactActivityDelegate


class PIPActivity : ReactActivity() {
    override fun getMainComponentName(): String = "pipactivity"
    override fun createReactActivityDelegate(): ReactActivityDelegate =
        DefaultReactActivityDelegate(this, mainComponentName, false)

    fun resumeMainActivity() {
        val intent = Intent(baseContext, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        baseContext.startActivity(intent)
    }

    override fun onPictureInPictureModeChanged(
        isInPictureInPictureMode: Boolean,
        newConfig: Configuration
    ) {
        super.onPictureInPictureModeChanged(isInPictureInPictureMode, newConfig)
        val pipModule = PIPModule.getInstance()

        if (!isInPictureInPictureMode) {
            if (pipModule != null) {
                resumeMainActivity()
                pipModule.sendEvent("onPiPModeChanged", null)
                // This just pauses playback in the main container
                finish()
            } else {
                // figure out how to log
            }
        }
    }

    override fun onStop() {
        super.onStop()
        val pipModule = PIPModule.getInstance()
        if (isFinishing) {
            if (pipModule != null) {
                resumeMainActivity()
                pipModule.sendEvent("onPiPModeChanged", null)
            } else {
                // figure out how to log
            }
        }
    }
}
