#!/bin/bash -e

HOMEBREW_NO_AUTO_UPDATE=1 brew list jq &>/dev/null || brew install jq
HOMEBREW_NO_AUTO_UPDATE=1 brew list gnu-sed &>/dev/null || brew install gnu-sed 
HOMEBREW_NO_AUTO_UPDATE=1 brew list xcbeautify &>/dev/null || brew install xcbeautify 
HOMEBREW_NO_AUTO_UPDATE=1 brew list libplist &>/dev/null || brew install libplist

project_path=$1

if [ "$build_environment" = "ci" ]; then
    temp_dir="$project_path"
else
    temp_dir="$project_path/temp"
fi

build_path="$PWD/build"
config_path="$project_path/devops/distribution.config.json"



permissionArgs=()

apptile_api_endpoint=$(jq -r '.apptile_api_endpoint' "$project_path/devops/distribution.config.json")
analytics_api_endpoint=$(jq -r '.analytics_api_endpoint' "$project_path/devops/distribution.config.json")
app_id=$(jq -r '.app_id' "$project_path/devops/distribution.config.json")
app_name=$(jq -r '.app_name' "$project_path/devops/distribution.config.json")
bundle_id=$(jq -r '.ios.bundle_id' "$project_path/devops/distribution.config.json")
current_project_version=$(jq -r '.ios.version_number' "$project_path/devops/distribution.config.json")
marketing_version=$(jq -r '.ios.version_semver' "$project_path/devops/distribution.config.json")
url_scheme=$(jq -r '.url_scheme' "$project_path/devops/distribution.config.json")
app_host=$(jq -r '.app_host' "$project_path/devops/distribution.config.json")
app_host_2=$(jq -r '.app_host_2' "$project_path/devops/distribution.config.json")
icon_path=$(jq -r '.ios.icon_path' "$project_path/devops/distribution.config.json")
splash_path=$(jq -r '.ios.splash_path' "$project_path/devops/distribution.config.json")
image_placeholder_path=$(jq -r '.ios.image_placeholder_path' "$project_path/devops/distribution.config.json")
update_logo_path=$(jq -r '.ios.update_logo_path' "$project_path/devops/distribution.config.json")
service_file_path=$(jq -r '.ios.service_file_path' "$project_path/devops/distribution.config.json")
team_id=$(jq -r '.ios.team_id' "$project_path/devops/distribution.config.json")
enable_apple_pay=$(jq -r '.ios.enableApplePay' "$project_path/devops/distribution.config.json")
enableAppTrackingTransparency=$(jq -r '.ios.enableAppTrackingTransparency' "$project_path/devops/distribution.config.json")
appTrackingTransparencyMessage=$(jq -r '.ios.trackingPermissionMessage' "$project_path/devops/distribution.config.json")
fb_appId=$(jq -r '.fb_appId' "$project_path/devops/distribution.config.json")
fb_clientToken=$(jq -r '.fb_clientToken' "$project_path/devops/distribution.config.json")
moengage_appId=$(jq -r '.moengage_appId' "$project_path/devops/distribution.config.json")
moengage_datacenter=$(jq -r '.moengage_datacenter' "$project_path/devops/distribution.config.json")
appsflyer_devKey=$(jq -r '.appsflyer_devKey' "$project_path/devops/distribution.config.json")
appsflyer_appId=$(jq -r '.appsflyer_appId' "$project_path/devops/distribution.config.json")
cleverTap_id=$(jq -r '.cleverTap_id' "$project_path/devops/distribution.config.json")
cleverTap_token=$(jq -r '.cleverTap_token' "$project_path/devops/distribution.config.json")
cleverTap_region=$(jq -r '.cleverTap_region' "$project_path/devops/distribution.config.json")
onesignal_appId=$(jq -r '.onesignal_appId' "$project_path/devops/distribution.config.json")
klaviyo_company_id=$(jq -r '.klaviyo_company_id' "$project_path/devops/distribution.config.json")
# apptile_base_framework_version=$(jq -r '.version' "$project_path/package.json")
apptile_base_framework_version=$currentFrameworkVersion
enableSourceMap=$(jq -r '.ios.enableSourceMap' "$project_path/devops/distribution.config.json")
sentry_sample_rate=$(jq -r '.sentry_sample_rate' "$config_path")
uploadToTestflight=$(jq -r '.ios.uploadToTestflight' "$project_path/devops/distribution.config.json")
use_global_placeholders=$(jq -r '.use_global_placeholders' "$config_path")
apptile_analytics_segment_key=$(jq -r '.apptile_analytics_segment_key' "$config_path")
enable_lively=$(jq -r '.ios.enableLively' "$project_path/devops/distribution.config.json")
cameraPermissionMessage=$(jq -r '.ios.cameraPermissionMessage' "$project_path/devops/distribution.config.json")
locationPermissionMessage=$(jq -r '.ios.locationPermissionMessage' "$project_path/devops/distribution.config.json")
microphonePermissionMessage=$(jq -r '.ios.microphonePermissionMessage' "$project_path/devops/distribution.config.json")
fireworkAppId=$(jq -r '.ios.fireworkAppId' "$project_path/devops/distribution.config.json")
enableNativeSplash=$(jq -r '.ios.enableNativeSplash' "$project_path/devops/distribution.config.json")
minSplashDuration=$(jq -r '.ios.minSplashDuration' "$project_path/devops/distribution.config.json")
enableLivelyPIP=$(jq -r '.ios.enableLivelyPIP' "$project_path/devops/distribution.config.json")

if [ -z "$apiKey" ] || [ "$apiKey" = "null" ] || 
   [ -z "$apiIssuerId" ] || [ "$apiIssuerId" = "null" ] || 
   [ -z "$authKeyPath" ] || [ "$authKeyPath" = "null" ]; then

  apiKey=$(jq -r '.ios.apiKey' "$project_path/devops/distribution.config.json")
  apiIssuerId=$(jq -r '.ios.issuerId' "$project_path/devops/distribution.config.json")
  authKeyPath=$(jq -r '.ios.appStorePrivateKey' "$project_path/devops/distribution.config.json")
  
fi

echo $apiKey $apiIssuerId $authKeyPath

export SENTRY_SAMPLE_RATE=$sentry_sample_rate
export APPTILE_ANALYTICS_SEGMENT_KEY=$apptile_analytics_segment_key
export USE_GLOBAL_PLACEHOLDERS=$use_global_placeholders

if [[ "$enableSourceMap" == "true" ]]
then
  grep -q "token=" ~/.sentryclirc || { echo -e "\033[0;31mAuth Token does not exist in ~/.sentryclirc to upload source-maps to sentry. Either disable enableSourceMap flag in distribution.config.json or else refer https://docs.sentry.io/product/cli/configuration to setup authentication\033[0m"; exit 1; }
  npm list -g @sentry/cli >/dev/null 2>&1 || npm install -g @sentry/cli
fi


echo -e "\n\033[0;36m----------------------iOS Prod App Build Script (running in $PWD)----------------------\033[0m\n"



if [ "$build_environment" != "ci" ]; then

echo -e "\n\n🧹 Cleaning temp directory...\n"
rm -rf $temp_dir/
mkdir -p $temp_dir/
cd $temp_dir/
echo -e "\n\n📑 Copying original files in \033[0;35m$temp_dir\033[0m \n"
cp ../{app.json,babel.config.js,index.js,package.json,package-lock.json,tsconfig.json,PIPActivityRoot.tsx} .
cp -R ../{app,ios,node_modules,web,scripts,patches} .
cp $project_path/devops/metro.config.js ./metro.config.js
cp $project_path/devops/react-native.config.js ./react-native.config.js

fi

cp $service_file_path ./ios/GoogleService-Info.plist
echo "{\"APPTILE_ANALYTICS_SEGMENT_KEY\": \"$apptile_analytics_segment_key\"}" > .env.json


echo -e "\n\n♻️ Replacing content in original files...\n"
npm install -g replace-in-file@4.0.0

plutil -replace APPTILE_BASE_FRAMEWORK_VERSION -string $apptile_base_framework_version ./ios/ReactNativeTSProject/Info.plist

plutil -replace APPTILE_UPDATE_ENDPOINT -string $APPTILE_UPDATE_ENDPOINT ./ios/ReactNativeTSProject/Info.plist

plutil -replace APPTILE_APP_FORK -string $fork_name ./ios/ReactNativeTSProject/Info.plist

plutil -replace APPTILE_BUNDLED_SAVE_ID -string $published_commit_id ./ios/ReactNativeTSProject/Info.plist

plutil -replace ANALYTICS_API_ENDPOINT -string $analytics_api_endpoint ./ios/ReactNativeTSProject/Info.plist
plutil -replace APPTILE_APP_ID -string $app_id ./ios/ReactNativeTSProject/Info.plist
plutil -replace APPTILE_IS_DISTRIBUTED_APP -string 1 ./ios/ReactNativeTSProject/Info.plist
if [[ -n "$url_scheme" && "$url_scheme" != "null" ]]; then
  plutil -replace APPTILE_URL_SCHEME -string "$url_scheme://" ./ios/ReactNativeTSProject/Info.plist
else
  plutil -replace APPTILE_URL_SCHEME -string "" ./ios/ReactNativeTSProject/Info.plist
fi
plutil -replace APPTILE_APP_HOST -string "https://$app_host" ./ios/ReactNativeTSProject/Info.plist
if [[ -n "$app_host_2" && "$app_host_2" != "" ]]; then
plutil -replace APPTILE_APP_HOST_2 -string  "https://$app_host_2" ./ios/ReactNativeTSProject/Info.plist
fi
replace-in-file "/<string>com.apptile.apptilepreviewdemo</string>/g" "<string>$bundle_id</string>" ./ios/ReactNativeTSProject/Info.plist --isRegex
if [[ -n "$url_scheme" && "$url_scheme" != "null" ]]; then
  plutil -replace CFBundleURLTypes.0.CFBundleURLSchemes -json "[\"$url_scheme\"]" ./ios/ReactNativeTSProject/Info.plist
else
  plutil -replace CFBundleURLTypes.0.CFBundleURLSchemes -json '[]' ./ios/ReactNativeTSProject/Info.plist
fi
plutil -replace NSAppTransportSecurity.NSExceptionDomains -dictionary ./ios/ReactNativeTSProject/Info.plist
plutil -insert "com\.apple\.developer\.associated-domains" -string "applinks:$app_host" -append ./ios/ReactNativeTSProject/ReactNativeTSProject.entitlements
if [[ -n "$app_host_2" && "$app_host_2" != "" ]]; then
plutil -insert "com\.apple\.developer\.associated-domains" -string "applinks:$app_host_2" -append ./ios/ReactNativeTSProject/ReactNativeTSProject.entitlements
fi
plutil -insert "com\.apple\.developer\.associated-domains" -string "applinks:$app_host" -append ./ios/ReactNativeTSProject/ReactNativeTSProjectRelease.entitlements
if [[ -n "$app_host_2" && "$app_host_2" != "" ]]; then
plutil -insert "com\.apple\.developer\.associated-domains" -string "applinks:$app_host_2" -append ./ios/ReactNativeTSProject/ReactNativeTSProjectRelease.entitlements
fi
replace-in-file "/<string>group.com.apptile.apptilepreviewdemo.notification</string>/g" "<string>group.$bundle_id.notification</string>" ./ios/ReactNativeTSProject/ReactNativeTSProject.entitlements --isRegex
replace-in-file "/<string>group.com.apptile.apptilepreviewdemo.notification</string>/g" "<string>group.$bundle_id.notification</string>" ./ios/ReactNativeTSProject/ReactNativeTSProjectRelease.entitlements --isRegex
replace-in-file "/<string>group.com.apptile.apptilepreviewdemo.notification</string>/g" "<string>group.$bundle_id.notification</string>" ./ios/ImageNotification/ImageNotification.entitlements --isRegex
replace-in-file "/<string>group.com.apptile.apptilepreviewdemo.notification</string>/g" "<string>group.$bundle_id.notification</string>" ./ios/NotificationContentExtension/NotificationContentExtension.entitlements --isRegex

# TODO: Enable iPad support with the enableIpadSupport flag in distribution.config.json

echo -e "\n\n⏳ Updating ios project file...\n"

npm i xcode -D
node $project_path/devops/scripts/ios/update-project-file.js $PWD/ios/ReactNativeTSProject.xcodeproj/project.pbxproj 1
replace-in-file "/DEVELOPMENT_TEAM = [A-Z0-9]{10}/g" "DEVELOPMENT_TEAM = $team_id" ./ios/ReactNativeTSProject.xcodeproj/project.pbxproj --isRegex


echo -e "\n\n⏳ Updating app name to \033[0;35m$app_name\033[0m and bundle ID to \033[0;35m$bundle_id\033[0m \n"

plutil -replace CFBundleDisplayName -string "$app_name" ./ios/ReactNativeTSProject/Info.plist
replace-in-file "/\"displayName\": \".{1,32}/g" "\"displayName\": \"$app_name\"" ./app.json --isRegex
replace-in-file "/com.apptile.apptilepreviewdemo/g" $bundle_id ./ios/ReactNativeTSProject.xcodeproj/project.pbxproj --isRegex


echo -e "\n\n⏳ Updating app version code to \033[0;35m$current_project_version\033[0m and version name to \033[0;35m$marketing_version\033[0m \n"

replace-in-file "/CURRENT_PROJECT_VERSION = [1-9]{1,4}/g" "CURRENT_PROJECT_VERSION = $current_project_version" ./ios/ReactNativeTSProject.xcodeproj/project.pbxproj --isRegex
replace-in-file "/MARKETING_VERSION = .{1,12}/g" "MARKETING_VERSION = $marketing_version;" ./ios/ReactNativeTSProject.xcodeproj/project.pbxproj --isRegex


echo -e " \n\n⚙️ Generating Splash from image at path \033[0;35m$splash_path\033[0m \n"

file_type=$(file -b $splash_path)
if [[ $file_type == *"GIF"* ]]
then
  # convert $splash_path[0] $temp_dir/first_frame.png
  # $project_path/devops/scripts/ios/imageset-generator.sh $temp_dir/first_frame.png $temp_dir/ios/ReactNativeTSProject
  cp $splash_path $temp_dir/app/assets/splash.gif
  npx --yes replace-in-file "/splash\.png/g" "splash.gif" $temp_dir/app/App.tsx --isRegex
  rm $temp_dir/app/assets/splash.png
elif  [[ $file_type == *"PNG"* ]]
then
   $project_path/devops/scripts/ios/imageset-generator.sh $splash_path $temp_dir/ios/ReactNativeTSProject
   cp $splash_path $temp_dir/app/assets/splash.png
fi

if [[ ( -n "$enableNativeSplash" && "$enableNativeSplash" == "true" ) ]]
then
  echo -e "Enabling native splash screen"
  replace-in-file "/\/\/ \#define ENABLE_NATIVE_SPLASH/" "#define ENABLE_NATIVE_SPLASH" ./ios/ReactNativeTSProject/AppDelegate.h --isRegex

  file_type=$(file -b $splash_path)
  if [[ $file_type == *"GIF"* ]]
  then
    HOMEBREW_NO_AUTO_UPDATE=1 brew list ffmpeg &>/dev/null || brew install ffmpeg
    replace-in-file "/\/\/ \#define ENABLE_NATIVE_SPLASH_WITH_GIF/" "#define ENABLE_NATIVE_SPLASH_WITH_GIF" ./ios/ReactNativeTSProject/AppDelegate.h --isRegex
    if [ -z "$minSplashDuration" ] || [ "$minSplashDuration" = "null" ];
    then
      minSplashDuration=$(ffprobe -v error -select_streams v:0 -show_entries stream=duration -of default=noprint_wrappers=1:nokey=1 $splash_path)
      echo "minSplashDuration for file found using ffmpeg: $minSplashDuration"
    fi
    ffmpeg -y -i $splash_path -vf "select=eq(n\,0)" -vsync vfr -q:v 2 $temp_dir/ios/splash.png
    $project_path/devops/scripts/ios/imageset-generator.sh $temp_dir/ios/splash.png $temp_dir/ios/ReactNativeTSProject
    cp $splash_path $temp_dir/ios/splash.gif
    replace-in-file "/\#define MIN_SPLASH_DURATION 1\.0/" "#define MIN_SPLASH_DURATION $minSplashDuration" ./ios/ReactNativeTSProject/AppDelegate.h --isRegex
  else 
    cp $splash_path $temp_dir/ios/splash.png
  fi

  mv ./ios/ReactNativeTSProject/nativestoryboard.replacement ./ios/ReactNativeTSProject/LaunchScreen.storyboard
  replace-in-file "/let isSplashHiddenAtStart \= false;/" "let isSplashHiddenAtStart = true;" $temp_dir/../../apptile-core/components/common/JSSplashScreen.tsx --isRegex
fi

echo -e " \n\n⚙️ Generating app icon from image at path \033[0;35m$icon_path\033[0m \n"

cp $icon_path $temp_dir/ios/icon.png
$project_path/devops/scripts/ios/iconset-generator.sh $icon_path $temp_dir/ios/ReactNativeTSProject


if [[ -n "$image_placeholder_path" && "$image_placeholder_path" != "null" ]]
then
   echo -e " \n\n⚙️ Replacing image placeholder from image at path \033[0;35m$image_placeholder_path\033[0m \n"
   cp -R $image_placeholder_path ./app/assets/image-placeholder.png
fi


if [[ -n "$update_logo_path" && "$update_logo_path" != "null" ]]
then
   echo -e " \n\n⚙️ Replacing update logo from image at path \033[0;35m$update_logo_path\033[0m \n"
   cp -R $update_logo_path ./app/assets/update-logo.png
fi

if [[ -n "$update_logo_path" && "$update_logo_path" != "null" ]]; then

  echo -e "\n\n⚙️ Replacing update logo from image at path \033[0;35m$update_logo_path\033[0m\n"
  
  if [[ $file_type_update_logo == *"GIF"* ]]; then
    cp -R "$update_logo_path" ./app/assets/update-logo.gif
  else
    npx --yes replace-in-file "/update-logo\.gif/g" "update-logo.png" "$temp_dir/app/App.tsx" --isRegex
    cp -R "$update_logo_path" ./app/assets/update-logo.png
  fi
  
fi


if [[ $enable_apple_pay == "false" || $enable_apple_pay == "null" ]]
then
  echo -e "\n\n📦 Disabling ApplePay...\n"
  plutil -remove "com\.apple\.developer\.in-app-payments" ./ios/ReactNativeTSProject/ReactNativeTSProjectRelease.entitlements
  plutil -remove "com\.apple\.developer\.in-app-payments" ./ios/ReactNativeTSProject/ReactNativeTSProject.entitlements
  # TODO: Remove ApplePay from other target entitlements
fi


if [[ $usingCamera == "false" ]]
then
  echo -e "\n\n📦 Disabling Camera...\n"
  npm uninstall react-native-camera
  mv ./app/plugins/widgets/CameraWidget/RNCamera.replacement.tsx ./app/plugins/widgets/CameraWidget/RNCamera.tsx
   if [[ $enable_lively != "true" ]]
    then
   plutil -remove NSCameraUsageDescription ./ios/ReactNativeTSProject/Info.plist
   gsed -i '/Permission-Camera/s/^ ./  # /' ./ios/Podfile
    fi
fi

if [[ ( -n "$fb_appId" && "$fb_appId" != "null" ) && ( -n "$fb_clientToken" && "$fb_clientToken" != "null" ) ]]
then
  echo -e "\n\n📦 Enabling FB SDK...\n"
  replace-in-file "//\* ForFBIntegration \(Don't remove\) /g" "" ./ios/ReactNativeTSProject/AppDelegate.mm --isRegex
  replace-in-file "/ ForFBIntegrationEnd \*//g" "" ./ios/ReactNativeTSProject/AppDelegate.mm --isRegex
  plutil -insert "FacebookAppID" -string "$fb_appId" -append ./ios/ReactNativeTSProject/info.plist
  plutil -insert "FacebookClientToken" -string "$fb_clientToken" -append ./ios/ReactNativeTSProject/info.plist
  plutil -insert "FacebookDisplayName" -string "$app_name" -append ./ios/ReactNativeTSProject/info.plist
  plutil -insert "FacebookAutoLogAppEventsEnabled" -bool YES -append ./ios/ReactNativeTSProject/info.plist
  plutil -insert "FacebookAdvertiserIDCollectionEnabled" -bool NO -append ./ios/ReactNativeTSProject/info.plist
  mv ./app/common/ApptileAnalytics/facebookAnalytics/index.replacement.ts ./app/common/ApptileAnalytics/facebookAnalytics/index.ts
  npm i --save-exact react-native-fbsdk-next@13.1.3
else
   npm uninstall react-native-fbsdk-next@13.1.3
fi

if [[ ( -n "$moengage_appId" && "$moengage_appId" != "null" ) && ( -n "$moengage_datacenter" && "$moengage_datacenter" != "null" ) ]]
then
  echo -e "\n\n📦 Enabling MoEngage SDK...\n"
  replace-in-file "/# MoengageDependency \(Don't remove\) /g" "" ./ios/Podfile --isRegex
  replace-in-file "//\* MoengageDependency \(Don't remove\) /g" "" ./ios/ImageNotification/NotificationService.m --isRegex
  replace-in-file "/group.com.apptile.apptilepreviewdemo.notification/g" "group.$bundle_id.notification" ./ios/ImageNotification/NotificationService.m --isRegex
  replace-in-file "/ MoengageDependencyEnd \*//g" "" ./ios/ImageNotification/NotificationService.m --isRegex
  replace-in-file "/\[\[FIRMessaging extensionHelper\]/g" "// [[FIRMessaging extensionHelper]" ./ios/ImageNotification/NotificationService.m --isRegex
  replace-in-file "/// MoengageDependency \(Don't remove\) /g" "" ./ios/NotificationContentExtension/NotificationViewController.swift --isRegex
  replace-in-file "/group.com.apptile.apptilepreviewdemo.notification/g" "group.$bundle_id.notification" ./ios/NotificationContentExtension/NotificationViewController.swift --isRegex
  replace-in-file "//\* MoengageDependency \(Don't remove\) /g" "" ./ios/ReactNativeTSProject/AppDelegate.mm --isRegex
  replace-in-file "/ MoengageDependencyEnd \*//g" "" ./ios/ReactNativeTSProject/AppDelegate.mm --isRegex
  replace-in-file "/<MoEngageDatacenter>/g" $moengage_datacenter ./ios/ReactNativeTSProject/AppDelegate.mm --isRegex
  replace-in-file "/<MoEngageAppId>/g" $moengage_appId ./ios/ReactNativeTSProject/AppDelegate.mm --isRegex
  plutil -insert MoEngageAppDelegateProxyEnabled -bool false ./ios/ReactNativeTSProject/Info.plist
  plutil -insert MoEngage -xml "<dict><key>ENABLE_LOGS</key><false/><key>MoEngage_APP_ID</key><string>$moengage_appId</string><key>DATA_CENTER</key><string>DATA_CENTER_0$moengage_datacenter</string><key>APP_GROUP_ID</key><string>group.$bundle_id.notification</string></dict>" ./ios/ReactNativeTSProject/Info.plist
  plutil -replace NSExtension.NSExtensionAttributes.UNNotificationExtensionCategory -string "MOE_PUSH_TEMPLATE" ./ios/NotificationContentExtension/Info.plist
  plutil -replace NSExtension.NSExtensionAttributes.UNNotificationExtensionInitialContentSizeRatio -float 1.2 ./ios/NotificationContentExtension/Info.plist
  plutil -insert NSExtension.NSExtensionAttributes.UNNotificationExtensionUserInteractionEnabled -bool true ./ios/NotificationContentExtension/Info.plist
  plutil -insert NSExtension.NSExtensionAttributes.UNNotificationExtensionDefaultContentHidden -bool true ./ios/NotificationContentExtension/Info.plist
  mv ./app/common/ApptileAnalytics/moengageAnalytics/index.replacement.ts ./app/common/ApptileAnalytics/moengageAnalytics/index.ts
  mv ./app/common/ApptileAnalytics/moengageAnalytics/useMoEngage.replacement.ts ./app/common/ApptileAnalytics/moengageAnalytics/useMoEngage.ts
  replace-in-file "/<MoEngageAppId>/g" $moengage_appId ./app/common/ApptileAnalytics/moengageAnalytics/useMoEngage.ts --isRegex
  npm i --save-exact react-native-moengage@10.1.0
else
   echo -e "\n\n📦 MoEngage SDK not enabled. Checking if react-native-moengage@10.1.0 is installed for uninstallation...\n"
   
   # Check if react-native-moengage@10.1.0 is installed in node_modules
   if npm list react-native-moengage@10.1.0 --depth=0 >/dev/null 2>&1; then
      echo "react-native-moengage@10.1.0 found. Uninstalling..."
      npm uninstall react-native-moengage@10.1.0
   else
      echo "react-native-moengage@10.1.0 not found. No action taken."
   fi
fi

if [[ ( -n "$appsflyer_devKey" && "$appsflyer_devKey" != "null" ) && ( -n "$appsflyer_appId" && "$appsflyer_appId" != "null" ) ]]
then
   echo -e "\n\n📦 Enabling AppsFlyer SDK...\n"
   mv ./app/common/ApptileAnalytics/appsflyerAnalytics/index.replacement.ts ./app/common/ApptileAnalytics/appsflyerAnalytics/index.ts
   mv ./app/common/ApptileAnalytics/appsflyerAnalytics/initAppsFlyer.replacement.ts ./app/common/ApptileAnalytics/appsflyerAnalytics/initAppsFlyer.ts
   replace-in-file "/<appsflyer_devKey>/g" $appsflyer_devKey ./app/common/ApptileAnalytics/appsflyerAnalytics/initAppsFlyer.ts --isRegex
   replace-in-file "/<appsflyer_appId>/g" $appsflyer_appId ./app/common/ApptileAnalytics/appsflyerAnalytics/initAppsFlyer.ts --isRegex
   npm i --save-exact react-native-appsflyer@6.12.2
else
   echo -e "\n\n📦 AppsFlyer SDK not enabled. Checking if react-native-appsflyer@6.12.2 is installed for uninstallation...\n"
   
   # Check if react-native-appsflyer@6.12.2 is installed in node_modules
   if npm list react-native-appsflyer@6.12.2 --depth=0 >/dev/null 2>&1; then
      echo "react-native-appsflyer@6.12.2 found. Uninstalling..."
      npm uninstall react-native-appsflyer@6.12.2
   else
      echo "react-native-appsflyer@6.12.2 not found. No action taken."
   fi
fi


if [[ 
  ( ( -n "$fb_appId" && "$fb_appId" != "null" ) && ( -n "$fb_clientToken" && "$fb_clientToken" != "null" ) ) ||
  ( ( ( -n "$appsflyer_devKey" && "$appsflyer_devKey" != "null" ) && ( -n "$appsflyer_appId" && "$appsflyer_appId" != "null" ) ) || ( "$enableAppTrackingTransparency" == "true" ) )
]]
then
  mv ./app/common/AppleATTPermission/index.replacement.ts ./app/common/AppleATTPermission/index.ts
  # plutil -insert "NSUserTrackingUsageDescription" -string "Your privacy matters. We collect usage data to enhance your app experience. Rest assured, your information is handled securely and used solely for improvement." -append ./ios/ReactNativeTSProject/info.plist
  if [[ $enable_lively != "true" ]]
  then
    echo "Added App Tracking Message"
    permissionArgs+=("apptracking=\"$appTrackingTransparencyMessage\"")
  fi

  gsed -i '/Permission-AppTrackingTransparency/s/^ .#/ /' ./ios/Podfile
fi

if [[ -n "$onesignal_appId" && "$onesignal_appId" != "null" ]]
then
   echo -e "\n\n📦 Enabling OneSignal SDK...\n"
   replace-in-file "//\* OneSignalDependency \(Don't remove\) /g" "" ./ios/ImageNotification/NotificationService.m --isRegex
   replace-in-file "/ OneSignalDependencyEnd \*//g" "" ./ios/ImageNotification/NotificationService.m --isRegex
   replace-in-file "/\[\[FIRMessaging extensionHelper\]/g" "// [[FIRMessaging extensionHelper]" ./ios/ImageNotification/NotificationService.m --isRegex
   replace-in-file "/# OneSignalDependency \(Don't remove\) /g" "" ./ios/Podfile --isRegex
   #  replace-in-file "/OneSignalRequiresItToRemove \*\/ /g" "" ./ios/ReactNativeTSProject/AppDelegate.mm --isRegex
   plutil -insert OneSignal_app_groups_key -string "group.$bundle_id.notification" ./ios/NotificationContentExtension/Info.plist
  #  gsed -i '/\/\/ OneSignalRequiresItToRemove/d' ./app/App.tsx
   
   sed -i '' '/* OneSignalRequiresItToRemove */, /* OneSignalRequiresItToRemoveEnd */d' ./app/App.tsx
   sed -i '' '/* OneSignalRequiresItToRemove */, /* OneSignalRequiresItToRemoveEnd */d' ./ios/ReactNativeTSProject/AppDelegate.mm

   replace-in-file "/messaging\(\)\.requestPermission\(\); // RemoveRequestWhenOneSignalInitialized/g" "messaging().hasPermission();" ./app/common/firebase/notificationHelper.ios.tsx --isRegex
   mv ./app/common/ApptileAnalytics/onesignalAnalytics/index.replacement.ts ./app/common/ApptileAnalytics/onesignalAnalytics/index.ts
   mv ./app/common/ApptileAnalytics/onesignalAnalytics/initOneSignal.replacement.ts ./app/common/ApptileAnalytics/onesignalAnalytics/initOneSignal.ts
   replace-in-file "/<OneSignalAppId>/g" $onesignal_appId ./app/common/ApptileAnalytics/onesignalAnalytics/initOneSignal.ts --isRegex
   npm i --save-exact react-native-onesignal@5.2.0
else
   echo -e "\n\n📦 OneSignal SDK not enabled. Checking if react-native-onesignal@5.2.0 is installed for uninstallation...\n"
   
   # Check if react-native-onesignal@5.2.0 is installed in node_modules
   if npm list react-native-onesignal@5.2.0 --depth=0 >/dev/null 2>&1; then
      echo "react-native-onesignal@5.2.0 found. Uninstalling..."
      npm uninstall react-native-onesignal@5.2.0
   else
      echo "react-native-onesignal@5.2.0 not found. No action taken."
   fi
fi

if [[ -n "$klaviyo_company_id" && "$klaviyo_company_id" != "null" ]]
then
   echo -e "\n\n📦 Enabling Klaviyo Events...\n"
   replace-in-file "/const COMPANY_ID: string = '<KLAVIYO_COMPANY_ID>';/" "const COMPANY_ID: string = '$klaviyo_company_id';" ./app/common/ApptileAnalytics/klaviyoAnalytics/index.ts --isRegex

   echo -e "\n\n📦 Enabling Klaviyo Notifications...\n"
   replace-in-file "/const COMPANY_ID: string = '<KLAVIYO_COMPANY_ID>';/" "const COMPANY_ID: string = '$klaviyo_company_id';" ./app/common/ApptileAnalytics/klaviyoAnalytics/initKlaviyo.ts --isRegex
   mv ./ios/ImageNotification/NotificationService.replacement.txt ./ios/ImageNotification/NotificationService.m
   replace-in-file "/# KlaviyoDependency \(Don't remove\) /g" "" ./ios/Podfile --isRegex
  plutil -insert "APPTILE_DEFAULT_NOTIFICATION_TITLE" -string "$app_name" -append ./ios/ImageNotification/Info.plist
fi

if [[ ( -n "$cleverTap_id" && "$cleverTap_id" != "null" ) && ( -n "$cleverTap_token" && "$cleverTap_token" != "null" ) && ( -n "$cleverTap_region" && "$cleverTap_region" != "null" ) ]]
then
   echo -e "\n\n📦 Enabling CleverTap SDK...\n"
   mv ./app/common/ApptileAnalytics/cleverTapAnalytics/index.replacement.ts ./app/common/ApptileAnalytics/cleverTapAnalytics/index.ts
   mv ./app/common/ApptileAnalytics/cleverTapAnalytics/initCleverTap.replacement.ts ./app/common/ApptileAnalytics/cleverTapAnalytics/initCleverTap.ts
   plutil -insert "CleverTapAccountID" -string "$cleverTap_id" -append ./ios/ReactNativeTSProject/info.plist
   plutil -insert "CleverTapToken" -string "$cleverTap_token" -append ./ios/ReactNativeTSProject/info.plist
   plutil -insert "CleverTapRegion" -string "$cleverTap_region" -append ./ios/ReactNativeTSProject/info.plist
   plutil -replace 'NSExtension.NSExtensionPrincipalClass' -string 'CTNotificationServiceExtension' ./ios/ImageNotification/Info.plist 
   replace-in-file "/// ForCleverTap \(Don't remove\) /g" "" ./ios/ReactNativeTSProject/AppDelegate.h --isRegex
   replace-in-file "/// ForCleverTap \(Don't remove\) /g" "" ./ios/ReactNativeTSProject/AppDelegate.mm --isRegex
   replace-in-file "/\# ForCleverTap \(Don't remove\) /g" "" ./ios/Podfile --isRegex
   npm i --save-exact clevertap-react-native@1.2.1
else
   echo -e "\n\n📦 CleverTap SDK not enabled. Checking if clevertap-react-native@1.2.1 is installed for uninstallation...\n"
   
   # Check if clevertap-react-native@1.2.1 is installed in node_modules
   if npm list clevertap-react-native@1.2.1 --depth=0 >/dev/null 2>&1; then
      echo "clevertap-react-native@1.2.1 found. Uninstalling..."
      npm uninstall clevertap-react-native@1.2.1
   else
      echo "clevertap-react-native@1.2.1 not found. No action taken."
   fi
fi

if [[ ( -n "$fireworkAppId" && "$fireworkAppId" != "null" && "$fireworkAppId" != "" ) ]]
then
  echo -e "\n\n📦 Enabling Firework SDK...\n"
  mv ./app/plugins/state/FireworkGlobalPlugin/firework.replacement.ts ./app/plugins/state/FireworkGlobalPlugin/firework.ts
  plutil -insert "FireworkVideoAppID" -string "$fireworkAppId" -append ./ios/ReactNativeTSProject/info.plist
  replace-in-file "/\/\* FireworkDependency \(Don\'t remove\)/g" "" ./ios/ReactnativeTSProject/AppDelegate.mm --isRegex
  replace-in-file "/FireworkDependencyEnd \*\//g" "" ./ios/ReactnativeTSProject/AppDelegate.mm --isRegex
  replace-in-file "/\/\* FireworkDependency \(Don\'t remove\)/g" "" ./ios/FireworkBridge.swift --isRegex
  replace-in-file "/FireworkDependencyEnd \*\//g" "" ./ios/FireworkBridge.swift --isRegex
  replace-in-file "/\/\* FireworkDependency \(Don\'t remove\)/g" "" ./ios/ReactNativeTSProject-Bridging-Header.h --isRegex
  replace-in-file "/FireworkDependencyEnd \*\//g" "" ./ios/ReactNativeTSProject-Bridging-Header.h --isRegex
  replace-in-file "/\# FireworkDependency \(Don't remove\)/g" "" ./ios/ReactNativeTSProject-Bridging-Header.h --isRegex
  replace-in-file "/\# FireworkDependency \(Don't remove\) /g" "" ./ios/Podfile --isRegex
  plutil -insert "UIBackgroundModes.0" -string "audio" ./ios/ReactNativeTSProject/Info.plist
  npm i --save-exact react-native-firework-sdk@2.14.0
else
  echo -e "\n\n📦 Firework SDK not enabled. Checking if react-native-firework-sdk@2.14.0 is installed for uninstallation...\n"
  
  # Check if react-native-firework-sdk@2.14.0 is installed in node_modules
  if npm list react-native-firework-sdk@2.14.0 --depth=0 >/dev/null 2>&1; then
    echo "react-native-firework-sdk@2.14.0 found. Uninstalling..."
    npm uninstall react-native-firework-sdk@2.14.0
  else
    echo "react-native-firework-sdk@2.14.0 not found. No action taken."
  fi
fi



echo "Checking for lively dependency management"

if [[ -z $enable_lively || $enable_lively != "true" ]]; then
  echo "Replacing Files........"
  if mv ./app/plugins/widgets/LivelyLiveSellingWidget/index.replacement.tsx ./app/plugins/widgets/LivelyLiveSellingWidget/index.tsx; then
    echo "File moved successfully."
  else
    echo "Failed to move the file." >&2
    exit 1
  fi

  echo "Replacing Files........"
  if mv ./app/plugins/widgets/LivelyLiveSellingWidgetV2/index.replacement.tsx ./app/plugins/widgets/LivelyLiveSellingWidgetV2/index.tsx; then
    echo "File moved successfully."
  else
    echo "Failed to move the file." >&2
    exit 1
  fi

   if mv ./app/plugins/datasource/Lively/liveStreams.replacement.tsx ./app/plugins/datasource/Lively/liveStreams.tsx; then
    echo "File moved successfully."
  else
    echo "Failed to move the file." >&2
    exit 1
  fi

  echo "Uninstalling npm packages........"
  if npm uninstall lively-live-stream-rn; then
    echo "Uninstalled lively-live-stream-rn successfully."
  else
    echo "Failed to uninstall lively-live-stream-rn." >&2
    exit 1
  fi

  if npm uninstall zego-express-engine-reactnative; then
    echo "Uninstalled zego-express-engine-reactnative successfully."
  else
    echo "Failed to uninstall zego-express-engine-reactnative." >&2
    exit 1
  fi
fi



if [[ $enable_lively == "true" ]]
then
   echo -e "\n\n Enabling Lively...\n"

   echo "Installing Lively and Zego Dependencies...."
   npm install lively-live-stream-rn@1.5.2

   if [[ $enableLivelyPIP == "true" ]]
   then
     replace-in-file "/const enableLivelyPIP \= false/" "const enableLivelyPIP = true" ./react-native.config.js --isRegex
     replace-in-file "/const enableLivelyPIP \= false/" "const enableLivelyPIP = true" ./metro.config.js --isRegex
     replace-in-file "/const enableLivelyPIP \= false/" "const enableLivelyPIP = true" ./app/plugins/widgets/LivelyLiveSellingWidgetV2/index.tsx --isRegex
     plutil -insert "UIBackgroundModes.0" -string "audio" ./ios/ReactNativeTSProject/Info.plist
     replace-in-file "/\/\* For NativePip don't remove/g" "" ./PIPActivityRoot.tsx --isRegex
     replace-in-file "/for NativePip end \*\//g" "" ./PIPActivityRoot.tsx --isRegex
   else 
     npm install zego-express-engine-reactnative@3.14.5
   fi

  # HARDCODED MESSAGES FOR LIVELY. BUT IT SHOULD BE TAKEN FROM DISTRIBUTION.CONFIG. 
  #DOING IT SO THAT BUILD SYSTEM CAN BUILD LIVELY WITH ONE SETTING.

  # locationPermissionMessage="We use your location to provide location-based services and enhance your app experience."
  cameraPermissionMessage="Access camera for live streaming"
  microphonePermissionMessage="Microphone for Live Streaming"

  appTrackingTransparencyMessage="Opting in to tracking allows our App to provide personalized offering to you across a variety of channels. This helps us show you the products and recommendations that you would be most interested in."


  #  permissionArgs+=("location=\"$locationPermissionMessage\"")
   permissionArgs+=("camera=\"$cameraPermissionMessage\"")
   permissionArgs+=("microphone=\"$microphonePermissionMessage\"")

  if [[ $enableAppTrackingTransparency == "true" ]]
  then
      echo "Lively Custom Tracking Permission Message Added.."
      permissionArgs+=("apptracking=\"$appTrackingTransparencyMessage\"")
  fi


fi

echo "Adding these Permissions to plist file \\n..."

echo $permissionArgs

node $project_path/devops/scripts/ios/permissions.js "./ios/ReactNativeTSProject/Info.plist" "${permissionArgs[@]}"


echo "Install dependencies for app"
npm i --no-audit
# Build apptile-core locally if argument was passed
usingLocalCore=false
for arg in "$@"; do
    if [ "$arg" = "use-local-core" ]; then
        usingLocalCore=true
        break
    fi
done


echo -e "\n\n⏳ Building prod app bundle...\n"

watchman watch-del-all $PWD

if [[ "$enableSourceMap" == "true" ]]
then
    npx --yes react-native bundle --dev false --entry-file index.js --bundle-output ./ios/main.jsbundle --assets-dest ./ios --sourcemap-output ./ios/main.jsbundle.map --platform ios


    echo -e "\nUploading Source Map to Sentry Server...."

    cd "$temp_dir/ios"
    SENTRY_RELEASE="$bundle_id@$marketing_version+$current_project_version"
    SENTRY_DIST="$current_project_version"
    export SENTRY_PROJECT=apptile-core-editor
    export SENTRY_ORG=apptile-qv
    sentry-cli releases files "$SENTRY_RELEASE" upload-sourcemaps --dist "$SENTRY_DIST" --strip-prefix "$temp_dir" main.jsbundle main.jsbundle.map
    cd $temp_dir

    echo -e "\n\033[0;32m Uploading Source Map Successful \033[0m"
else
    npx --yes react-native bundle --dev false --entry-file index.js --bundle-output ./ios/main.jsbundle --assets-dest ./ios --platform ios
fi

replace-in-file "/let isSplashHiddenAtStart \= true;/" "let isSplashHiddenAtStart = false;" $temp_dir/../../apptile-core/components/common/JSSplashScreen.tsx --isRegex

echo -e "\n\n⏳ Installing pods...\n"

cd ios/
pod install --repo-update
gsed -i 's/source="\$(readlink "\${source}")"/source="\$(readlink -f "\${source}")"/' ./Pods/Target\ Support\ Files/Pods-ReactNativeTSProject/Pods-ReactNativeTSProject-frameworks.sh
cd $temp_dir/


echo -e "\n\n💼 Archiving project...\n"


if [ -z "$apiKey" ] || [ "$apiKey" = "null" ] || 
   [ -z "$apiIssuerId" ] || [ "$apiIssuerId" = "null" ] || 
   [ -z "$authKeyPath" ] || [ "$authKeyPath" = "null" ]; then

    echo "Authorizing through Xcode For Archiving..."

  archive_logs=$(RCT_NO_LAUNCH_PACKAGER=1 SKIP_BUNDLING=1 xcodebuild clean archive -sdk iphoneos -workspace $temp_dir/ios/ReactNativeTSProject.xcworkspace -scheme ReactNativeTSProject -archivePath $temp_dir/ios/ReactNativeTSProject.xcarchive -allowProvisioningUpdates | xcbeautify 2>&1 | tee >(cat - >&2))

else

    echo "Authorizing through apiKey, issuerId, P8 file For Archiving...."

   archive_logs=$(RCT_NO_LAUNCH_PACKAGER=1 SKIP_BUNDLING=1 xcodebuild clean archive -sdk iphoneos -workspace $temp_dir/ios/ReactNativeTSProject.xcworkspace -scheme ReactNativeTSProject -archivePath $temp_dir/ios/ReactNativeTSProject.xcarchive -allowProvisioningUpdates -authenticationKeyID "$apiKey" -authenticationKeyIssuerID "$apiIssuerId" -authenticationKeyPath "$authKeyPath" | xcbeautify 2>&1 | tee >(cat - >&2))
fi

echo -e "\n\n⚙️ Generating .ipa file...\n"

cp $project_path/devops/files/preview-app/ios/ExportOptions.plist $temp_dir
if [[ $env == "prod" ]]
then
  plutil -replace method -string "app-store-connect" $temp_dir/ExportOptions.plist
fi

mkdir -p $build_path/
plutil -replace teamID -string $team_id $temp_dir/ExportOptions.plist


if [ -z "$apiKey" ] || [ "$apiKey" = "null" ] || 
   [ -z "$apiIssuerId" ] || [ "$apiIssuerId" = "null" ] || 
   [ -z "$authKeyPath" ] || [ "$authKeyPath" = "null" ]; then

  echo "Authorizing Through Xcode For Generating IPA...."
   export_logs=$(xcodebuild -exportArchive -archivePath $temp_dir/ios/ReactNativeTSProject.xcarchive -exportPath $build_path -exportOptionsPlist $temp_dir/ExportOptions.plist -allowProvisioningUpdates | xcbeautify 2>&1 | tee >(cat - >&2))

else
  echo "Authorizing through Api Key, IssuerId, P8 File For Generating IPA...."
  
  export_logs=$(xcodebuild -exportArchive -archivePath $temp_dir/ios/ReactNativeTSProject.xcarchive -exportPath $build_path -exportOptionsPlist $temp_dir/ExportOptions.plist -allowProvisioningUpdates -authenticationKeyID "$apiKey" -authenticationKeyIssuerID "$apiIssuerId" -authenticationKeyPath "$authKeyPath" | xcbeautify 2>&1 | tee >(cat - >&2))

fi




echo "$export_logs" | grep -q "Export Succeeded"

echo -e "\n\033[0;32mSuccessfully built $build_path/ReactNativeTSProject.ipa! 🎉 🎉 🎉\033[0m"

if [ "$uploadToTestflight" = "true" ]; then

echo -e "Uploading to Testflight......"

if [ -z "$apiKey" ] || [ "$apiKey" = "null" ] || 
   [ -z "$apiIssuerId" ] || [ "$apiIssuerId" = "null" ] || 
   [ -z "$authKeyPath" ] || [ "$authKeyPath" = "null" ]; then

  echo "Auth Key or Api Key or ApiIssuer ID is missing.. aborting upload"
  exit 1
  

else
  echo "Authorizing through Api Key, IssuerId, P8 File For Uploading IPA...."
  
# xcrun altool command to upload to test flight
 xcrun altool --upload-app -f $project_path/build/ReactNativeTSProject.ipa -t ios --apiKey $apiKey --apiIssuer $apiIssuerId --verbose

fi  

fi


# cd $project_path && rm -rf $temp_dir/
