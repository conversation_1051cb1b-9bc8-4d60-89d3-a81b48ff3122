import type Toast from 'react-native-toast-notifications';
import type {logger as Logger} from 'apptile-core';
import websdk from './web/websdk';
import * as ReactRedux from 'react-redux';
import React from 'react';

declare global {
  var toast: Toast;
  var logger: typeof Logger;
  var sendLog: (message: string) => void;

  var apptileWebSDK: {
    status: 'loading' | 'success' | 'error' | 'notstarted';
    React?: typeof React;
    ReactRouter: any;
    ReactRouterDom: any;
    ApptileWebCore?: typeof websdk;
    ReactRedux?: typeof ReactRedux;
    moduleExports?: {
      components: {
        [key: string]: any;
      };
      functions: {
        [key: string]: (...args: any[]) => any;
      };
      dashboardRoutes: {
        [key: string]: {
          config: {
            isVisible: boolean;
            id: string;
            text: string;
            icon: string;
            iconType: string;
            isActive:boolean;
            isComingSoon:boolean;
          };
          component: any;
        };
      };
      flowConfig: {
        [key: string]: {
          isVisible: boolean;
          id: string;
          text: string;
          icon: string;
          iconType: string;
          isActive: boolean;
          dispatchAction: () => void;
        }
      },
      editorLeftSidebarItems: {

      },
      logRocketKey: string
    };
  };
  var reloadLively: () => void;
  var reloadShoppableFeeds: () => void;
  var PLUGIN_SERVER_URL: string;
  var WEB_SDK_ARTIFACT_ID: string;
}
