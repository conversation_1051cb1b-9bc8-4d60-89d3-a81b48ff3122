import React, {useEffect} from 'react';
import {View, Text, StyleSheet, ScrollView, Pressable, Image} from 'react-native';

import {CampaignCard, CreateCampaignCard} from './shared';
import {MaterialCommunityIcons} from 'apptile-core';
import {darkTheme} from './styles';
import {useParams} from '@/root/web/routing.web';
import {useNavigate} from 'react-router';
import {useDispatch, useSelector} from 'react-redux';
import {isEmpty} from 'lodash';

import moment from 'moment';
import {makeToast} from '@/root/web/actions/toastActions';
import {PushNotificationApi} from '@/root/web/api/PushNotificationApi';

import TextElement from '@/root/web/components-v2/base/TextElement';
import {IManualNotification, IPaginatedResponse, IAutomatedNotification} from './declaration';
import {OneSignalPushNotificationApi} from '../../api/OneSignalPushNotificationApi';
import {EditorRootState} from '../../store/EditorRootState';
import {oneSignalGetRecordsTransformer, oneSignalTemplatesTransformer} from './shared/oneSignalTransformer';

type DashboardProps = {};

const styles = StyleSheet.create({
  container: {
    backgroundColor: darkTheme.background,
    flex: 1,
  },
  rowLayout: {
    flexDirection: 'row',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignCenter: {
    alignItems: 'center',
  },
  historyIcon: {marginRight: 10},
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  contentContainer: {maxWidth: 1230, width: '95%', minWidth: 768, marginBottom: 50, paddingVertical: 40},
  toggleContainer: {
    marginTop: 20,
  },
  campaignList: {
    marginVertical: 20,
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  toggleText: {
    paddingBottom: 10,
    borderBottomWidth: 2,
    color: darkTheme.primaryText,
    marginRight: 20,
  },
  inactiveToggleText: {borderBottomColor: darkTheme.background, fontWeight: '500'},
  activeToggleText: {borderBottomColor: darkTheme.toggleActive, fontWeight: '600'},
  campaignBanner: {
    borderRadius: 10,
    overflow: 'hidden',
    marginVertical: 30,
    backgroundColor: darkTheme.bannerBackground,
    borderWidth: 1,
    borderColor: darkTheme.bannerBorder,
  },
  infoText: {
    fontFamily: "'Work Sans', sans-serif",
    fontSize: 14,
    color: darkTheme.primaryText,
    lineHeight: 16,
    fontWeight: '500',
  },
  bannerImage: {
    flex: 1,
    aspectRatio: 1044 / 228,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
    overflow: 'hidden',
    borderRadius: 10,
    marginHorizontal: '0.66%',
  },
  emptyCampaign: {
    height: 175,
    width: '100%',
    backgroundColor: darkTheme.emptyStateBackground,
    borderRadius: 10,
    marginBottom: 20,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderWidth: 1,
    borderColor: darkTheme.emptyStateBorder,
  },
  roundBorder: {
    borderWidth: 1,
    borderRadius: 40,
    paddingHorizontal: 8,
    paddingVertical: 5,
    borderColor: darkTheme.primaryBorder,
    backgroundColor: darkTheme.secondaryCardBackground,
  },
});

const Dashboard = React.forwardRef<View, DashboardProps>(() => {
  const navigate = useNavigate();
  const platformState = useSelector((state: EditorRootState) => state.platform);
  const {activeNotificationProvider} = platformState;
  useEffect(() => {
    if (activeNotificationProvider !== 'oneSignal' && activeNotificationProvider !== 'APPTILE') {
      navigate(`../notifications/unavailable/${activeNotificationProvider}`);
    }
  }, [activeNotificationProvider]);

  return (
    <View style={[styles.container]}>
      <ScrollView contentContainerStyle={styles.wrapper}>
        <View style={styles.contentContainer}>
          <Banner />
          <Campaigns />
          <Explore />
        </View>
      </ScrollView>
    </View>
  );
});

type ExploreProps = {};
const Explore: React.FC<ExploreProps> = () => {
  const navigate = useNavigate();
  const param = useParams();
  const dispatch = useDispatch();

  const [list, setList] = React.useState<IAutomatedNotification[]>([]);
  const [selectedNotify, setSelectedNotify] = React.useState<'PUSH' | 'IN_APP'>('PUSH');

  const appID = param.id as string;

  React.useEffect(() => {
    if (isEmpty(appID)) return;

    const listAutomatedNotification = async () => {
      try {
        //! await PushNotificationApi.createAutomatedRecord(appID);  --> refresh automated records
        const response = await PushNotificationApi.listAutomatedRecord<IAutomatedNotification[]>(appID);

        if (response.status >= 200 && response.status <= 299) {
          setList(response.data);
        } else {
          throw new Error();
        }
      } catch (err) {}
    };

    listAutomatedNotification();
  }, [dispatch, appID]);

  const onStartPress = (id: string) => {
    navigate(`../notifications/automated?notificationId=${id}`);
  };

  return (
    <>
      <TextElement color="SECONDARY" fontSize="3xl" lineHeight="3xl" fontWeight="600">
        Explore
      </TextElement>
      <View style={[styles.rowLayout, styles.alignCenter, styles.toggleContainer]}>
        <Pressable onPress={() => setSelectedNotify('PUSH')}>
          <TextElement
            color="SECONDARY"
            fontWeight="500"
            fontSize="md"
            style={[
              styles.toggleText,
              selectedNotify === 'PUSH' ? styles.activeToggleText : styles.inactiveToggleText,
            ]}>
            Push notification
          </TextElement>
        </Pressable>
      </View>
      <View style={[styles.campaignList]}>
        <CreateCampaignCard />
        {list
          .filter(e => e.disabled)
          .map(entry => (
            <CampaignCard
              heading={entry.metadata?.title || '-title-'}
              description={entry.metadata?.description || ''}
              key={entry.id}
              previewTitle={entry.title || ''}
              previewBody={entry.body || ''}
              previewImageUrl={entry.imageUrl || ''}
              onShowcasePress={() => onStartPress(entry.id)}
              onStartPress={() => onStartPress(entry.id)}
            />
          ))}
      </View>
    </>
  );
};

const Campaigns: React.FC = () => {
  const navigate = useNavigate();
  const param = useParams();
  const dispatch = useDispatch();
  const oneSignalAppId = useSelector((state: EditorRootState) => state.platform.oneSignalAppId);
  const hasOneSignal = useSelector((state: EditorRootState) => state.platform.hasOneSignal);

  const [manualList, setManualList] = React.useState<IManualNotification[]>([]);
  const [automatedList, setAutomatedList] = React.useState<IAutomatedNotification[]>([]);
  const [loading, setLoading] = React.useState(true);

  const appID = param.id;

  React.useEffect(() => {
    let listManualNotification;
    if (!hasOneSignal) {
      listManualNotification = async () => {
        try {
          if (!appID) return;
          const response = await PushNotificationApi.listManualRecord<IPaginatedResponse>(
            appID,
            3,
            0,
            'PENDING,DRAFT,LIVE',
          );

          if (response.status >= 200 && response.status <= 299) {
            setManualList(response.data.items);
            setLoading(false);
          } else {
            setLoading(false);
            throw new Error();
          }
        } catch (err) {}
      };
    } else {
      setManualList([]);
      listManualNotification = async () => {
        const notificationData = await OneSignalPushNotificationApi.getNotifications(oneSignalAppId, appID, 10, 0);
        // const templateData = await OneSignalPushNotificationApi.viewTemplates(oneSignalAppId, appID, 50, 0);
        const requiredNotifications = notificationData?.data?.notifications?.filter(
          n => !n.completed_at && !n.canceled,
        );
        // const requiredTemplates = templateData?.data?.templates?.filter(t => !t.name.startsWith('OneSignal Push:'));

        if (notificationData.status >= 200 && notificationData.status <= 299) {
          setManualList([
            ...oneSignalGetRecordsTransformer(requiredNotifications),
            // ...oneSignalTemplatesTransformer(requiredTemplates),
          ]);
          setLoading(false);
        } else {
          setLoading(false);
          throw new Error();
        }
      };
    }

    listManualNotification();
  }, [dispatch, appID, hasOneSignal, oneSignalAppId]);

  React.useEffect(() => {
    if (isEmpty(appID)) return;

    const listAutomatedNotification = async () => {
      try {
        const response = await PushNotificationApi.listAutomatedRecord<IAutomatedNotification[]>(appID);

        if (response.status >= 200 && response.status <= 299) {
          setAutomatedList(response.data.filter(e => !e.disabled));
        } else {
          throw new Error();
        }
      } catch (err) {}
    };

    if (!hasOneSignal) {
      listAutomatedNotification();
    }
  }, [dispatch, appID, hasOneSignal]);

  const onManualEditPress = (id: string) => {
    navigate(`../notifications/manual?action=edit&notificationId=${id}`);
  };

  const onDeletePress = async (id: string, status?: string) => {
    try {
      if (!appID) return;
      if (!hasOneSignal) {
        const response = await PushNotificationApi.deleteManualRecord(id, appID);
        setManualList(prev => prev.filter(entry => entry.id !== id));

        if (!(response.status >= 200 && response.status <= 299)) {
          throw new Error();
        }
      } else {
        let response;
        if (status === 'DRAFT') {
          response = await OneSignalPushNotificationApi.deleteTemplate(oneSignalAppId, appID, id);
        }
        if (status === 'PENDING') {
          response = await OneSignalPushNotificationApi.stopPushNotification(appID, id, oneSignalAppId);
        }
        setManualList(prev => prev.filter(entry => entry.id !== id));

        if (!(response.status >= 200 && response.status <= 299)) {
          throw new Error();
        }
      }
    } catch (err) {
      dispatch(
        makeToast({
          content: 'Not able to delete notification records',
          appearances: 'error',
        }),
      );
    }
  };

  const onStopPress = async (id: string) => {
    try {
      if (!appID) return;
      const response = await PushNotificationApi.stopLiveRecurringCampaign(id, appID);
      setManualList(prev => prev.filter(entry => entry.id !== id));

      if (!(response.status >= 200 && response.status <= 299)) {
        throw new Error();
      }
    } catch (err) {
      dispatch(
        makeToast({
          content: 'Cant able to stop notification campaign',
          appearances: 'error',
        }),
      );
    }
  };

  const onAutomatedEditPress = (id: string) => {
    navigate(`../notifications/automated?notificationId=${id}`);
  };

  const pauseAutomatedNotification = async (notificationID: string) => {
    try {
      setAutomatedList(prev => prev.filter(entry => entry.id !== notificationID));
      const updateAutomatedRecord = {
        disabled: true,
      };
      await PushNotificationApi.updateAutomatedRecord(notificationID, appID, updateAutomatedRecord);
    } catch (err) {}
  };

  const getActiveText = (status: string) => {
    if (status === 'LIVE') {
      return 'LIVE';
    }
    return status === 'PENDING' ? 'SCHEDULED' : 'DRAFT';
  };

  const getDescriptionText = entry => {
    let descriptionText = '';
    if (entry.deliveryFrequency === 'RECURRING') {
      descriptionText = 'Recurring Campaign - ';
      if (entry.status === 'LIVE') {
        return `${descriptionText}Started at ${moment(entry.triggeredAt).format('Do MMM, LT')}`;
      }
    }
    if (entry.scheduledAt != null) {
      return `${descriptionText}Scheduled for ${moment(entry.scheduledAt).format('Do MMM [at] LT')}`;
    }
    return '';
  };

  return (
    <View>
      <View style={[styles.rowLayout, styles.spaceBetween, styles.alignCenter]}>
        <TextElement color="SECONDARY" fontSize="3xl" lineHeight="3xl" fontWeight="600">
          Your Campaigns
        </TextElement>

        <View style={[styles.rowLayout, styles.alignCenter]}>
          <Pressable
            style={[styles.rowLayout, styles.spaceBetween, styles.alignCenter, styles.historyIcon, styles.roundBorder]}
            onPress={() => {
              navigate('../notifications/history');
            }}
            nativeID="historySection">
            <MaterialCommunityIcons name="history" size={18} color={darkTheme.iconActive} />
            <TextElement style={[styles.infoText, {marginLeft: 5}]} color="SECONDARY">
              Show history
            </TextElement>
          </Pressable>
          <Pressable
            style={[styles.rowLayout, styles.spaceBetween, styles.alignCenter, styles.roundBorder]}
            onPress={() => {
              navigate('../notifications/activeCampaign');
            }}
            nativeID="seeAllSection">
            <Text style={styles.infoText}>See all</Text>
            <MaterialCommunityIcons name="chevron-right" size={18} color={darkTheme.iconActive} />
          </Pressable>
        </View>
      </View>
      <View style={[styles.campaignList]}>
        {manualList.map((entry, idx) => (
          <CampaignCard
            heading={entry.title || '-title-'}
            description={getDescriptionText(entry)}
            key={idx}
            isActive
            iconName={getActiveText(entry.status) === 'LIVE' ? 'stop-circle-outline' : ''}
            activeText={getActiveText(entry.status)}
            previewTitle={entry.title || ''}
            previewBody={entry.body || ''}
            previewImageUrl={entry.imageUrl || ''}
            onShowcasePress={() => onManualEditPress(entry.id)}
            onEditPress={entry.status !== 'PENDING' ? () => onManualEditPress(entry.id) : null}
            onDeletePress={entry.status !== 'LIVE' ? () => onDeletePress(entry.id, entry.status) : null}
            onStopPress={entry.status === 'LIVE' ? () => onStopPress(entry.id) : null}
          />
        ))}
        {!hasOneSignal &&
          automatedList.map((entry, idx) => (
            <CampaignCard
              heading={entry.metadata?.title || '-title-'}
              description={entry.metadata?.description || ''}
              key={idx}
              isActive
              activeText="LIVE"
              iconName="stop-circle-outline"
              previewTitle={entry.title || ''}
              previewBody={entry.body || ''}
              previewImageUrl={entry.imageUrl || ''}
              onShowcasePress={() => onAutomatedEditPress(entry.id)}
              onEditPress={() => onAutomatedEditPress(entry.id)}
              onPausePress={() => pauseAutomatedNotification(entry.id)}
            />
          ))}
        {manualList.length === 0 && (hasOneSignal || automatedList.length === 0) && (
          <View style={styles.emptyCampaign}>
            {loading ? (
              <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
            ) : (
              <Text style={styles.infoText}>No Active Campaigns</Text>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

const Banner: React.FC = () => {
  return (
    <>
      <TextElement color="SECONDARY" fontSize="3xl" lineHeight="3xl" fontWeight="600">
        Marketing
      </TextElement>
      <View style={styles.campaignBanner}>
        <Image source={require('@/root/web/assets/images/notification_banner.png')} style={styles.bannerImage} />
      </View>
    </>
  );
};

export default Dashboard;
