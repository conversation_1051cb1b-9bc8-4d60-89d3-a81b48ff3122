import React, {useEffect} from 'react';
import {View, Text, StyleSheet, ScrollView, Pressable, Image} from 'react-native';

import {CampaignCard, CreateCampaignCard} from './shared';
import {MaterialCommunityIcons} from 'apptile-core';
import {darkTheme} from './styles';
import {useParams} from '@/root/web/routing.web';
import {useNavigate} from 'react-router';
import {useDispatch, useSelector} from 'react-redux';
import {isEmpty} from 'lodash';

import moment from 'moment';
import {makeToast} from '@/root/web/actions/toastActions';
import {PushNotificationApi} from '@/root/web/api/PushNotificationApi';

import TextElement from '@/root/web/components-v2/base/TextElement';
import {IManualNotification, IPaginatedResponse, IAutomatedNotification} from './declaration';
import {OneSignalPushNotificationApi} from '../../api/OneSignalPushNotificationApi';
import {EditorRootState} from '../../store/EditorRootState';
import {oneSignalGetRecordsTransformer, oneSignalTemplatesTransformer} from './shared/oneSignalTransformer';

// Dummy data for testing the design
const DUMMY_ACTIVE_CAMPAIGNS: IManualNotification[] = [
  {
    id: 'dummy-1',
    title: 'Exclusive collection is calling you',
    body: "Be the first to shop and get 20% off everything. Don't miss it! 🛍️",
    imageUrl: 'https://via.placeholder.com/60x60/4A90E2/FFFFFF?text=👤',
    status: 'LIVE',
    deliveryFrequency: 'ONCE',
    triggeredAt: new Date().toISOString(),
    scheduledAt: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'dummy-2',
    title: 'Back in Stock',
    body: "Your favorite items are back! Shop now before they're gone again.",
    imageUrl: 'https://via.placeholder.com/60x60/50C878/FFFFFF?text=📦',
    status: 'DRAFT',
    deliveryFrequency: 'ONCE',
    triggeredAt: null,
    scheduledAt: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'dummy-3',
    title: 'Customer Winback',
    body: 'We miss you! Come back and enjoy 15% off your next purchase.',
    imageUrl: 'https://via.placeholder.com/60x60/FF6B6B/FFFFFF?text=💝',
    status: 'LIVE',
    deliveryFrequency: 'RECURRING',
    triggeredAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    scheduledAt: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

const DUMMY_SCHEDULED_CAMPAIGNS: IManualNotification[] = [
  {
    id: 'dummy-scheduled-1',
    title: 'Exclusive collection is calling you',
    body: "Be the first to shop and get 20% off everything. Don't miss it! 🛍️",
    imageUrl: 'https://via.placeholder.com/60x60/4A90E2/FFFFFF?text=👤',
    status: 'PENDING',
    deliveryFrequency: 'ONCE',
    triggeredAt: null,
    scheduledAt: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'dummy-scheduled-2',
    title: 'Flash Sale Alert',
    body: 'Get ready for our biggest sale of the year! 50% off everything.',
    imageUrl: 'https://via.placeholder.com/60x60/FFD700/FFFFFF?text=⚡',
    status: 'PENDING',
    deliveryFrequency: 'ONCE',
    triggeredAt: null,
    scheduledAt: new Date(Date.now() + 172800000).toISOString(), // Day after tomorrow
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'dummy-scheduled-3',
    title: 'Weekend Special',
    body: 'Weekend vibes with special discounts just for you!',
    imageUrl: 'https://via.placeholder.com/60x60/9B59B6/FFFFFF?text=🎉',
    status: 'PENDING',
    deliveryFrequency: 'ONCE',
    triggeredAt: null,
    scheduledAt: new Date(Date.now() + 259200000).toISOString(), // 3 days from now
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
];

type DashboardProps = {};

const styles = StyleSheet.create({
  container: {
    backgroundColor: darkTheme.background,
    flex: 1,
  },
  rowLayout: {
    flexDirection: 'row',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignCenter: {
    alignItems: 'center',
  },
  historyIcon: {marginRight: 10},
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  contentContainer: {maxWidth: 1230, width: '95%', minWidth: 768, marginBottom: 50, paddingVertical: 40},
  toggleContainer: {
    marginTop: 20,
  },
  campaignList: {
    marginVertical: 20,
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  toggleText: {
    paddingBottom: 10,
    borderBottomWidth: 2,
    color: darkTheme.primaryText,
    marginRight: 20,
  },
  inactiveToggleText: {borderBottomColor: darkTheme.background, fontWeight: '500'},
  activeToggleText: {borderBottomColor: darkTheme.toggleActive, fontWeight: '600'},
  campaignBanner: {
    borderRadius: 10,
    overflow: 'hidden',
    marginVertical: 30,
    backgroundColor: darkTheme.bannerBackground,
    borderWidth: 1,
    borderColor: darkTheme.bannerBorder,
  },
  infoText: {
    fontFamily: "'Work Sans', sans-serif",
    fontSize: 14,
    color: darkTheme.primaryText,
    lineHeight: 16,
    fontWeight: '500',
  },
  bannerImage: {
    flex: 1,
    aspectRatio: 1044 / 228,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
    overflow: 'hidden',
    borderRadius: 10,
    marginHorizontal: '0.66%',
  },
  emptyCampaign: {
    height: 175,
    width: '100%',
    backgroundColor: darkTheme.emptyStateBackground,
    borderRadius: 10,
    marginBottom: 20,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderWidth: 1,
    borderColor: darkTheme.emptyStateBorder,
  },
  roundBorder: {
    borderWidth: 1,
    borderRadius: 40,
    paddingHorizontal: 8,
    paddingVertical: 5,
    borderColor: darkTheme.primaryBorder,
    backgroundColor: darkTheme.secondaryCardBackground,
  },
  // New styles for Figma design
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 40,
  },
  headerContent: {
    flex: 1,
  },
  mainTitle: {
    fontSize: 32,
    fontWeight: '600',
    color: darkTheme.primaryText,
    marginBottom: 8,
    lineHeight: 40,
  },
  subtitle: {
    fontSize: 16,
    color: darkTheme.secondaryText,
    lineHeight: 24,
  },
  createButton: {
    backgroundColor: darkTheme.primaryButton,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  createButtonText: {
    color: darkTheme.primaryButtonText,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  sectionContainer: {
    marginBottom: 40,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: darkTheme.primaryText,
    lineHeight: 28,
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  seeAllText: {
    fontSize: 14,
    color: darkTheme.primaryText,
    marginRight: 4,
  },
  campaignGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
});

const Dashboard = React.forwardRef<View, DashboardProps>(() => {
  const navigate = useNavigate();
  const platformState = useSelector((state: EditorRootState) => state.platform);
  const {activeNotificationProvider} = platformState;
  useEffect(() => {
    if (activeNotificationProvider !== 'oneSignal' && activeNotificationProvider !== 'APPTILE') {
      navigate(`../notifications/unavailable/${activeNotificationProvider}`);
    }
  }, [activeNotificationProvider]);

  return (
    <View style={[styles.container]}>
      <ScrollView contentContainerStyle={styles.wrapper}>
        <View style={styles.contentContainer}>
          <Header />
          <ActiveCampaigns />
          <ScheduledCampaigns />
          <HistorySection />
        </View>
      </ScrollView>
    </View>
  );
});

// Header Component - Push Notifications title with Create New button
const Header: React.FC = () => {
  const navigate = useNavigate();

  const handleCreateNew = () => {
    navigate(`../notifications/manual?action=create`);
  };

  return (
    <View style={styles.headerContainer}>
      <View style={styles.headerContent}>
        <Text style={styles.mainTitle}>Push Notifications</Text>
        <Text style={styles.subtitle}>Boost customer retention with Notifications</Text>
      </View>
      <Pressable style={styles.createButton} onPress={handleCreateNew}>
        <MaterialCommunityIcons name="plus" size={16} color={darkTheme.primaryButtonText} />
        <Text style={styles.createButtonText}>Create New</Text>
      </Pressable>
    </View>
  );
};

// Active Campaigns Component
const ActiveCampaigns: React.FC = () => {
  const navigate = useNavigate();
  const param = useParams();
  const dispatch = useDispatch();
  const oneSignalAppId = useSelector((state: EditorRootState) => state.platform.oneSignalAppId);
  const hasOneSignal = useSelector((state: EditorRootState) => state.platform.hasOneSignal);

  const [manualList, setManualList] = React.useState<IManualNotification[]>([]);
  const [automatedList, setAutomatedList] = React.useState<IAutomatedNotification[]>([]);
  const [loading, setLoading] = React.useState(true);

  const appID = param.id;

  React.useEffect(() => {
    let listManualNotification;
    if (!hasOneSignal) {
      listManualNotification = async () => {
        try {
          if (!appID) return;
          const response = await PushNotificationApi.listManualRecord<IPaginatedResponse>(
            appID,
            3,
            0,
            'PENDING,DRAFT,LIVE',
          );

          if (response.status >= 200 && response.status <= 299) {
            setManualList(response.data.items);
            setLoading(false);
          } else {
            setLoading(false);
            throw new Error();
          }
        } catch (err) {}
      };
    } else {
      setManualList([]);
      listManualNotification = async () => {
        const notificationData = await OneSignalPushNotificationApi.getNotifications(oneSignalAppId, appID, 10, 0);
        const requiredNotifications = notificationData?.data?.notifications?.filter(
          n => !n.completed_at && !n.canceled,
        );

        if (notificationData.status >= 200 && notificationData.status <= 299) {
          setManualList([...oneSignalGetRecordsTransformer(requiredNotifications)]);
          setLoading(false);
        } else {
          setLoading(false);
          throw new Error();
        }
      };
    }

    listManualNotification();
  }, [dispatch, appID, hasOneSignal, oneSignalAppId]);

  React.useEffect(() => {
    if (isEmpty(appID)) return;

    const listAutomatedNotification = async () => {
      try {
        const response = await PushNotificationApi.listAutomatedRecord<IAutomatedNotification[]>(appID);

        if (response.status >= 200 && response.status <= 299) {
          setAutomatedList(response.data.filter(e => !e.disabled));
        } else {
          throw new Error();
        }
      } catch (err) {}
    };

    if (!hasOneSignal) {
      listAutomatedNotification();
    }
  }, [dispatch, appID, hasOneSignal]);

  const onManualEditPress = (id: string) => {
    navigate(`../notifications/manual?action=edit&notificationId=${id}`);
  };

  const onDeletePress = async (id: string, status?: string) => {
    try {
      if (!appID) return;
      if (!hasOneSignal) {
        const response = await PushNotificationApi.deleteManualRecord(id, appID);
        setManualList(prev => prev.filter(entry => entry.id !== id));

        if (!(response.status >= 200 && response.status <= 299)) {
          throw new Error();
        }
      } else {
        let response;
        if (status === 'DRAFT') {
          response = await OneSignalPushNotificationApi.deleteTemplate(oneSignalAppId, appID, id);
        }
        if (status === 'PENDING') {
          response = await OneSignalPushNotificationApi.stopPushNotification(appID, id, oneSignalAppId);
        }
        setManualList(prev => prev.filter(entry => entry.id !== id));

        if (!(response.status >= 200 && response.status <= 299)) {
          throw new Error();
        }
      }
    } catch (err) {
      dispatch(
        makeToast({
          content: 'Not able to delete notification records',
          appearances: 'error',
        }),
      );
    }
  };

  const onStopPress = async (id: string) => {
    try {
      if (!appID) return;
      const response = await PushNotificationApi.stopLiveRecurringCampaign(id, appID);
      setManualList(prev => prev.filter(entry => entry.id !== id));

      if (!(response.status >= 200 && response.status <= 299)) {
        throw new Error();
      }
    } catch (err) {
      dispatch(
        makeToast({
          content: 'Cant able to stop notification campaign',
          appearances: 'error',
        }),
      );
    }
  };

  const onAutomatedEditPress = (id: string) => {
    navigate(`../notifications/automated?notificationId=${id}`);
  };

  const pauseAutomatedNotification = async (notificationID: string) => {
    try {
      setAutomatedList(prev => prev.filter(entry => entry.id !== notificationID));
      const updateAutomatedRecord = {
        disabled: true,
      };
      await PushNotificationApi.updateAutomatedRecord(notificationID, appID, updateAutomatedRecord);
    } catch (err) {}
  };

  const getActiveText = (status: string) => {
    if (status === 'LIVE') {
      return 'LIVE';
    }
    return status === 'PENDING' ? 'SCHEDULED' : 'DRAFT';
  };

  const getDescriptionText = entry => {
    let descriptionText = '';
    if (entry.deliveryFrequency === 'RECURRING') {
      descriptionText = 'Recurring Campaign - ';
      if (entry.status === 'LIVE') {
        return `${descriptionText}Started at ${moment(entry.triggeredAt).format('Do MMM, LT')}`;
      }
    }
    if (entry.scheduledAt != null) {
      return `${descriptionText}Scheduled for ${moment(entry.scheduledAt).format('Do MMM [at] LT')}`;
    }
    return '';
  };

  // Filter active campaigns (LIVE and DRAFT)
  const activeCampaigns = manualList.filter(entry => entry.status === 'LIVE' || entry.status === 'DRAFT');

  return (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Active Campaigns</Text>
        <Pressable style={styles.seeAllButton} onPress={() => navigate('../notifications/activeCampaign')}>
          <Text style={styles.seeAllText}>See all</Text>
          <MaterialCommunityIcons name="chevron-right" size={16} color={darkTheme.iconPrimary} />
        </Pressable>
      </View>
      <View style={styles.campaignGrid}>
        {activeCampaigns.map((entry, idx) => (
          <CampaignCard
            heading={entry.title || '-title-'}
            description={getDescriptionText(entry)}
            key={idx}
            isActive
            iconName={getActiveText(entry.status) === 'LIVE' ? 'stop-circle-outline' : ''}
            activeText={getActiveText(entry.status)}
            previewTitle={entry.title || ''}
            previewBody={entry.body || ''}
            previewImageUrl={entry.imageUrl || ''}
            onShowcasePress={() => onManualEditPress(entry.id)}
            onEditPress={entry.status !== 'PENDING' ? () => onManualEditPress(entry.id) : undefined}
            onDeletePress={entry.status !== 'LIVE' ? () => onDeletePress(entry.id, entry.status) : undefined}
            onStopPress={entry.status === 'LIVE' ? () => onStopPress(entry.id) : undefined}
          />
        ))}
        {!hasOneSignal &&
          automatedList.map((entry, idx) => (
            <CampaignCard
              heading={entry.metadata?.title || '-title-'}
              description={entry.metadata?.description || ''}
              key={`auto-${idx}`}
              isActive
              activeText="LIVE"
              iconName="stop-circle-outline"
              previewTitle={entry.title || ''}
              previewBody={entry.body || ''}
              previewImageUrl={entry.imageUrl || ''}
              onShowcasePress={() => onAutomatedEditPress(entry.id)}
              onEditPress={() => onAutomatedEditPress(entry.id)}
              onPausePress={() => pauseAutomatedNotification(entry.id)}
            />
          ))}
        {activeCampaigns.length === 0 && (hasOneSignal || automatedList.length === 0) && (
          <View style={styles.emptyCampaign}>
            {loading ? (
              <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
            ) : (
              <Text style={styles.infoText}>No Active Campaigns</Text>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

// Scheduled Campaigns Component
const ScheduledCampaigns: React.FC = () => {
  const navigate = useNavigate();
  const param = useParams();
  const dispatch = useDispatch();
  const oneSignalAppId = useSelector((state: EditorRootState) => state.platform.oneSignalAppId);
  const hasOneSignal = useSelector((state: EditorRootState) => state.platform.hasOneSignal);

  const [manualList, setManualList] = React.useState<IManualNotification[]>([]);
  const [loading, setLoading] = React.useState(true);

  const appID = param.id;

  React.useEffect(() => {
    let listManualNotification;
    if (!hasOneSignal) {
      listManualNotification = async () => {
        try {
          if (!appID) return;
          const response = await PushNotificationApi.listManualRecord<IPaginatedResponse>(appID, 3, 0, 'PENDING');

          if (response.status >= 200 && response.status <= 299) {
            setManualList(response.data.items);
            setLoading(false);
          } else {
            setLoading(false);
            throw new Error();
          }
        } catch (err) {}
      };
    } else {
      setManualList([]);
      listManualNotification = async () => {
        const notificationData = await OneSignalPushNotificationApi.getNotifications(oneSignalAppId, appID, 10, 0);
        const requiredNotifications = notificationData?.data?.notifications?.filter(
          n => !n.completed_at && !n.canceled && n.send_after,
        );

        if (notificationData.status >= 200 && notificationData.status <= 299) {
          setManualList([...oneSignalGetRecordsTransformer(requiredNotifications)]);
          setLoading(false);
        } else {
          setLoading(false);
          throw new Error();
        }
      };
    }

    listManualNotification();
  }, [dispatch, appID, hasOneSignal, oneSignalAppId]);

  const onManualEditPress = (id: string) => {
    navigate(`../notifications/manual?action=edit&notificationId=${id}`);
  };

  const onDeletePress = async (id: string, status?: string) => {
    try {
      if (!appID) return;
      if (!hasOneSignal) {
        const response = await PushNotificationApi.deleteManualRecord(id, appID);
        setManualList(prev => prev.filter(entry => entry.id !== id));

        if (!(response.status >= 200 && response.status <= 299)) {
          throw new Error();
        }
      } else {
        let response;
        if (status === 'DRAFT') {
          response = await OneSignalPushNotificationApi.deleteTemplate(oneSignalAppId, appID, id);
        }
        if (status === 'PENDING') {
          response = await OneSignalPushNotificationApi.stopPushNotification(appID, id, oneSignalAppId);
        }
        setManualList(prev => prev.filter(entry => entry.id !== id));

        if (!(response.status >= 200 && response.status <= 299)) {
          throw new Error();
        }
      }
    } catch (err) {
      dispatch(
        makeToast({
          content: 'Not able to delete notification records',
          appearances: 'error',
        }),
      );
    }
  };

  const getDescriptionText = entry => {
    if (entry.scheduledAt != null) {
      return `Scheduled for ${moment(entry.scheduledAt).format('Do MMM [at] LT')}`;
    }
    return '';
  };

  // Filter scheduled campaigns (PENDING)
  const scheduledCampaigns = manualList.filter(entry => entry.status === 'PENDING');

  return (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Scheduled</Text>
        <Pressable style={styles.seeAllButton} onPress={() => navigate('../notifications/activeCampaign')}>
          <Text style={styles.seeAllText}>See all</Text>
          <MaterialCommunityIcons name="chevron-right" size={16} color={darkTheme.iconPrimary} />
        </Pressable>
      </View>
      <View style={styles.campaignGrid}>
        {scheduledCampaigns.map((entry, idx) => (
          <CampaignCard
            heading={entry.title || '-title-'}
            description={getDescriptionText(entry)}
            key={idx}
            isActive
            activeText="SCHEDULED"
            previewTitle={entry.title || ''}
            previewBody={entry.body || ''}
            previewImageUrl={entry.imageUrl || ''}
            onShowcasePress={() => onManualEditPress(entry.id)}
            onEditPress={() => onManualEditPress(entry.id)}
            onDeletePress={() => onDeletePress(entry.id, entry.status)}
          />
        ))}
        {scheduledCampaigns.length === 0 && (
          <View style={styles.emptyCampaign}>
            {loading ? (
              <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
            ) : (
              <Text style={styles.infoText}>No Scheduled Campaigns</Text>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

// History Section Component
const HistorySection: React.FC = () => {
  const navigate = useNavigate();

  return (
    <View style={styles.sectionContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>History</Text>
        <Pressable style={styles.seeAllButton} onPress={() => navigate('../notifications/history')}>
          <Text style={styles.seeAllText}>See all</Text>
          <MaterialCommunityIcons name="chevron-right" size={16} color={darkTheme.iconPrimary} />
        </Pressable>
      </View>
    </View>
  );
};

export default Dashboard;
