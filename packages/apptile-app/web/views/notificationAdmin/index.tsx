import React from 'react';
import {Route, Routes} from 'react-router';
import Dashboard from './dashBoard';
import CustomNotification from './manualNotification/playground';
import AutomatedNotification from './automatedNotification/playground';
import LiveCampaign from './liveCampaign';
import History from './history';
import Outcomes from './outcomes';
import NotificationUnavailable from './NotificationUnavailable';

export const NotificationRouter: React.FC = () => {
  return (
    <Routes>
      <Route path="notifications" element={<Dashboard />} />
      <Route path="notifications/manual" element={<CustomNotification />} />
      <Route path="notifications/automated" element={<AutomatedNotification />} />
      <Route path="notifications/activeCampaign" element={<LiveCampaign />} />
      <Route path="notifications/history" element={<History />} />
      <Route path="notifications/outcomes/:notificationId" element={<Outcomes />} />
      <Route path="notifications/unavailable/:notificationId" element={<NotificationUnavailable />} />
    </Routes>
  );
};
