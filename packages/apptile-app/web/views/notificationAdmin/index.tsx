import React from 'react';
import {Route, Routes} from 'react-router';
import Dashboard from './dashBoard';
import CustomNotification from './manualNotification/playground';
import AutomatedNotification from './automatedNotification/playground';
import LiveCampaign from './liveCampaign';
import History from './history';
import Outcomes from './outcomes';
import NotificationUnavailable from './NotificationUnavailable';
import {darkTheme} from './styles';
import LeftSidebar from '../prompt-to-app/dashboard/LeftSidebar';
import {NavigationProvider} from '@/root/web/contexts/NavigationContext';

const NotificationLayout: React.FC<{children: React.ReactNode}> = ({children}) => {
  return (
    <NavigationProvider>
      <div style={styles.container}>
        <div style={styles.mainContainer}>
          <LeftSidebar mainBar="APP_EDITOR" />
          <div style={styles.rightContainer}>
            <div style={styles.content}>{children}</div>
          </div>
        </div>
      </div>
    </NavigationProvider>
  );
};

export const NotificationRouter: React.FC = () => {
  return (
    <NotificationLayout>
      <Routes>
        <Route path="notifications" element={<Dashboard />} />
        <Route path="notifications/manual" element={<CustomNotification />} />
        <Route path="notifications/automated" element={<AutomatedNotification />} />
        <Route path="notifications/activeCampaign" element={<LiveCampaign />} />
        <Route path="notifications/history" element={<History />} />
        <Route path="notifications/outcomes/:notificationId" element={<Outcomes />} />
        <Route path="notifications/unavailable/:notificationId" element={<NotificationUnavailable />} />
      </Routes>
    </NotificationLayout>
  );
};

const styles = {
  container: {
    display: 'flex' as const,
    flexDirection: 'column' as const,
    height: '100vh',
    overflow: 'hidden' as const,
    backgroundColor: darkTheme.background,
  },
  mainContainer: {
    display: 'flex' as const,
    flexDirection: 'row' as const,
    height: '100%',
    maxHeight: '100vh',
    flex: 1,
    overflow: 'hidden' as const,
  },
  rightContainer: {
    display: 'flex' as const,
    flexDirection: 'column' as const,
    flex: 1,
    borderLeftWidth: 1,
    borderLeftColor: darkTheme.primaryBorder,
    borderLeftStyle: 'solid' as const,
    height: '100%',
    overflow: 'hidden' as const,
  },
  content: {
    flex: 1,
    backgroundColor: darkTheme.background,
    display: 'flex' as const,
    flexDirection: 'column' as const,
    overflow: 'hidden' as const,
    minHeight: 0,
  },
};
