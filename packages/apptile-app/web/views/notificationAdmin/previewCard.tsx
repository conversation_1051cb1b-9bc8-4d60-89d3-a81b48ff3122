import React from 'react';
import {View, StyleSheet, Image, ImageSourcePropType} from 'react-native';

import TextElement from '@/root/web/components-v2/base/TextElement';
import {darkTheme} from './styles';

type IPreviewCard = {
  logoUrl?: string;
  title?: string;
  description?: string;
  imageUrl?: string;
};

const styles = StyleSheet.create({
  cardImage: {
    width: '100%',
    height: '100%',
  },
  previewCard: {
    width: '100%',
    overflow: 'hidden',
    backgroundColor: darkTheme.playgroundPreview,
    borderRadius: 10,
    padding: 10,
    borderWidth: 1,
    borderColor: darkTheme.playgroundBorder,
    shadowColor: darkTheme.background,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 2,
  },
  title: {
    flexShrink: 1,
    color: darkTheme.primaryText,
    marginBottom: 5,
  },
  description: {
    color: darkTheme.secondaryText,
    marginBottom: 10,
    flexShrink: 1,
  },
  contentContainer: {justifyContent: 'space-between', alignItems: 'center', flexDirection: 'row'},
  imgContainer: {
    width: 50,
    height: 50,
    borderRadius: 4,
    margin: 5,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
  },
  row: {
    flexDirection: 'row',
  },
});

export const PreviewCard: React.FC<IPreviewCard> = props => {
  return (
    <View style={styles.previewCard}>
      <View style={styles.contentContainer}>
        <View style={styles.imgContainer}>
          <Image
            style={styles.cardImage}
            resizeMode="cover"
            source={require('@/root/web/assets/images/logo-placeholder.png')}
          />
        </View>
        <View style={{flex: 1}}>
          <View style={[styles.row]}>
            <TextElement fontSize="sm" fontWeight="500" style={styles.title}>
              {props.title || 'Exclusive collection is calling you 💕'}
            </TextElement>
          </View>
          <View style={[styles.row]}>
            <TextElement fontSize="xs" style={styles.description}>
              {props.description || "Be the first to shop and get 20% off everything. Don't miss it! 👀✌️"}
            </TextElement>
          </View>
        </View>
        {!!props.imageUrl && (
          <View style={styles.imgContainer}>
            <Image style={styles.cardImage} resizeMode="cover" source={props.imageUrl as ImageSourcePropType} />
          </View>
        )}
      </View>
    </View>
  );
};
