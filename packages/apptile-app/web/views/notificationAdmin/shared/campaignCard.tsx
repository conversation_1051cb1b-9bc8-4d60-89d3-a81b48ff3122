import React from 'react';
import {View, Text, StyleSheet, Pressable, StyleProp, ViewStyle} from 'react-native';
import {useNavigate} from 'react-router';

import {MaterialCommunityIcons} from 'apptile-core';
import Button from '@/root/web/components-v2/base/Button';
import {darkTheme} from '../styles';

import TextElement from '@/root/web/components-v2/base/TextElement';
import {PreviewCard} from '../previewCard';

const styles = StyleSheet.create({
  heading: {
    color: darkTheme.primaryText,
  },
  description: {
    marginTop: 5,
    lineHeight: 20,
    color: darkTheme.secondaryText,
  },
  alignCenter: {
    alignItems: 'center',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  campaignCardContainer: {
    width: '32%',
    padding: 8,
    marginHorizontal: '0.66%',
  },
  campaignShowcase: {
    height: 291,
    backgroundColor: darkTheme.campaignCardBackground,
    borderRadius: 24,
    marginBottom: 20,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderWidth: 1,
    borderColor: darkTheme.campaignCardBorder,
  },
  createCampaignButton: {
    width: 85,
    height: 85,
    borderRadius: 50,
    backgroundColor: darkTheme.secondaryCardBackground,
    marginBottom: 25,
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
  },
  indicatorContainer: {
    position: 'absolute',
    zIndex: 1,
    top: 10,
    right: 10,
    backgroundColor: darkTheme.cardBackground,
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 35,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: darkTheme.primaryBorder,
  },
  indicator: {
    color: darkTheme.primaryText,
  },
  popUpMenu: {
    position: 'absolute',
    backgroundColor: darkTheme.modalBackground,
    borderColor: darkTheme.primaryBorder,
    borderWidth: 1,
    width: 230,
    borderRadius: 20,
    bottom: 30,
    right: -10,
    padding: 10,
  },
  relative: {
    position: 'relative',
  },
  menuItem: {
    width: 100,
    paddingHorizontal: 10,
    paddingVertical: 10,
    color: darkTheme.primaryText,
  },
  button: {
    width: 80,
  },
  popUpContainer: {
    width: 40,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
});

type CampaignCardProps = {
  heading: string;
  description: string;
  previewTitle: string;
  previewBody: string;
  previewImageUrl: string;
  isActive?: boolean;
  isHistory?: boolean;
  activeText?: 'DRAFT' | 'LIVE' | 'SCHEDULED';
  iconName?: string;
  fromOutcomes?: boolean;
  onEditPress?: () => void;
  onDeletePress?: () => void;
  onStartPress?: () => void;
  onPausePress?: () => void;
  onStopPress?: () => void;
  onShowcasePress?: () => void;
  wrapperStyles?: StyleProp<ViewStyle>;
  campaignShowcaseStyles?: StyleProp<ViewStyle>;
};

export const CampaignCard = React.forwardRef<View, CampaignCardProps>((props, ref) => {
  const {
    heading,
    description,
    isActive,
    previewTitle,
    isHistory,
    previewBody,
    previewImageUrl,
    activeText,
    fromOutcomes,
    wrapperStyles,
    campaignShowcaseStyles,
  } = props;
  const [isMenuVisible, setMenuVisible] = React.useState(false);

  const onEditPress = () => {
    props.onEditPress && props.onEditPress();
    setMenuVisible(false);
  };
  const onDeletePress = () => {
    props.onDeletePress && props.onDeletePress();
    setMenuVisible(false);
  };
  const onPausePress = () => {
    props.onPausePress && props.onPausePress();
    setMenuVisible(false);
  };

  const onStopPress = () => {
    props.onStopPress && props.onStopPress();
    setMenuVisible(false);
  };

  const statusColor = activeText === 'LIVE' ? darkTheme.notificationLive : darkTheme.notificationScheduled;

  const [showEdit, setShowEdit] = React.useState(false);

  return (
    <View ref={ref} style={[styles.campaignCardContainer, wrapperStyles]}>
      <Pressable
        onMouseEnter={() => setShowEdit(true)}
        onMouseLeave={() => setShowEdit(false)}
        onPress={props.onShowcasePress}>
        <View style={[styles.campaignShowcase, campaignShowcaseStyles]}>
          {isActive && (
            <View style={styles.indicatorContainer}>
              {props.iconName && (
                <MaterialCommunityIcons name={props.iconName} size={16} color={statusColor} style={{marginRight: 3}} />
              )}
              <TextElement fontSize="xs" style={[styles.indicator, {color: statusColor}]}>
                {activeText}
              </TextElement>
            </View>
          )}
          <PreviewCard title={previewTitle} description={previewBody} imageUrl={previewImageUrl} />
        </View>

        {showEdit && (
          <View
            style={{
              position: 'absolute',
              top: 13,
              left: 15,
              backgroundColor: darkTheme.cardBackground,
              padding: 5,
              borderRadius: 50,
              borderWidth: 1,
              borderColor: darkTheme.primaryBorder,
            }}>
            <MaterialCommunityIcons name="pencil" size={20} color={darkTheme.iconActive} />
          </View>
        )}
      </Pressable>
      <View style={[styles.relative, styles.contentContainer]}>
        {!fromOutcomes && (
          <View style={{flex: 1}}>
            <TextElement fontSize="md" lineHeight="xl" fontWeight="500" style={styles.heading}>
              {heading}
            </TextElement>
            <TextElement fontSize="sm" style={styles.description}>
              {description}
            </TextElement>
          </View>
        )}
        {isActive ? (
          <View style={styles.popUpContainer}>
            <Pressable onPress={() => setMenuVisible(prev => !prev)}>
              <MaterialCommunityIcons name="dots-horizontal" size={25} color={darkTheme.iconPrimary} />
            </Pressable>
            {isMenuVisible && (
              <View style={styles.popUpMenu}>
                {props.onEditPress && (
                  <Pressable onPress={onEditPress}>
                    <Text style={styles.menuItem}>Edit</Text>
                  </Pressable>
                )}
                {props.onDeletePress && (
                  <Pressable onPress={onDeletePress}>
                    <Text style={styles.menuItem}>Delete</Text>
                  </Pressable>
                )}
                {props.onStopPress && (
                  <Pressable onPress={onStopPress}>
                    <Text style={styles.menuItem}>Stop</Text>
                  </Pressable>
                )}
                {props.onPausePress && (
                  <Pressable onPress={onPausePress}>
                    <Text style={styles.menuItem}>Pause</Text>
                  </Pressable>
                )}
              </View>
            )}
          </View>
        ) : (
          <>
            {!isHistory && (
              <View style={styles.button}>
                <Button variant="PILL" onPress={() => props.onStartPress && props.onStartPress()} color="PRIMARY">
                  START
                </Button>
              </View>
            )}
          </>
        )}
      </View>
    </View>
  );
});

export const CreateCampaignCard = () => {
  const navigate = useNavigate();
  const onPress = () => {
    navigate(`../notifications/manual?action=create`);
  };

  return (
    <View style={styles.campaignCardContainer}>
      <Pressable onPress={onPress}>
        <View style={[styles.campaignShowcase]}>
          <View
            style={[styles.createCampaignButton, styles.alignCenter, styles.justifyCenter]}
            nativeID="createCampaignIcon">
            <MaterialCommunityIcons name="plus" size={35} color={darkTheme.iconPrimary} />
          </View>
          <TextElement fontSize="md" lineHeight="xl" fontWeight="500" color="SECONDARY">
            Create Your Own
          </TextElement>
        </View>
      </Pressable>
      <View style={[styles.contentContainer]}>
        <View style={{flex: 1}}>
          <TextElement fontSize="md" lineHeight="xl" fontWeight="500" style={styles.heading}>
            Custom
          </TextElement>
          <TextElement fontSize="sm" style={styles.description}>
            Create a new scheduled or instant notification
          </TextElement>
        </View>
        <View style={styles.button}>
          <Button variant="PILL" onPress={onPress} color="PRIMARY">
            START
          </Button>
        </View>
      </View>
    </View>
  );
};
