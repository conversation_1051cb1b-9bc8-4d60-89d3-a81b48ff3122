// Dark theme styles for Notification Admin
export const darkTheme = {
  // Main backgrounds
  background: '#000000',
  headerBackground: '#1A1D20',
  cardBackground: '#1A1D20',
  secondaryCardBackground: '#292B32',
  modalBackground: '#2A2A2A',

  // Text colors
  primaryText: '#FFFFFF',
  secondaryText: '#B0B0B0',
  tertiaryText: '#808080',

  // Border colors
  primaryBorder: '#333333',
  secondaryBorder: '#2A2A2A',
  activeBorder: '#1060E0',
  whiteBorder: 'white',

  // Button colors
  primaryButton: '#1060E0',
  primaryButtonText: '#FFFFFF',
  secondaryButton: '#333333',
  secondaryButtonText: '#FFFFFF',
  disabledButton: '#404040',
  disabledButtonText: '#666666',

  // Input colors
  inputBackground: '#292B32',
  inputBorder: '#404040',
  inputText: '#FFFFFF',
  inputPlaceholder: '#808080',

  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',

  // Campaign card colors
  campaignCardBackground: '#1A1A1A',
  campaignCardBorder: '#333333',
  campaignActiveBackground: '#1060E0',
  campaignInactiveBackground: '#404040',

  // Banner colors
  bannerBackground: '#1A1D20',
  bannerBorder: '#333333',

  // Toggle colors
  toggleActive: '#1060E0',
  toggleInactive: '#404040',
  toggleText: '#FFFFFF',

  // Icon colors
  iconPrimary: '#FFFFFF',
  iconSecondary: '#B0B0B0',
  iconActive: '#1060E0',

  // Loading colors
  loadingBackground: '#1A1D20',
  loadingText: '#B0B0B0',

  // Empty state colors
  emptyStateBackground: '#2A2A2A',
  emptyStateText: '#808080',
  emptyStateBorder: '#404040',

  // Notification specific colors
  notificationLive: '#4CAF50',
  notificationScheduled: '#FF9800',
  notificationDraft: '#808080',
  notificationPending: '#1060E0',

  // History colors
  historyCardBackground: '#1A1D20',
  historyCardBorder: '#333333',
  historyStatusSuccess: '#4CAF50',
  historyStatusFailed: '#F44336',
  historyStatusPending: '#FF9800',

  // Playground colors
  playgroundBackground: '#1A1D20',
  playgroundSidebar: '#292B32',
  playgroundPreview: '#000000',
  playgroundBorder: '#333333',

  // Form colors
  formBackground: '#1A1D20',
  formFieldBackground: '#292B32',
  formFieldBorder: '#404040',
  formFieldText: '#FFFFFF',
  formLabel: '#B0B0B0',
  formError: '#F44336',
  formSuccess: '#4CAF50',

  // Dropdown colors
  dropdownBackground: '#292B32',
  dropdownBorder: '#404040',
  dropdownText: '#FFFFFF',
  dropdownHover: '#333333',
  dropdownSelected: '#1060E0',

  // Tooltip colors
  tooltipBackground: '#2A2A2A',
  tooltipText: '#FFFFFF',
  tooltipBorder: '#404040',

  // Scroll colors
  scrollbarTrack: '#1A1D20',
  scrollbarThumb: '#404040',
  scrollbarThumbHover: '#555555',
};

// Light theme for comparison/fallback
export const lightTheme = {
  background: '#FFFFFF',
  headerBackground: '#FFFFFF',
  cardBackground: '#FFFFFF',
  modalBackground: '#FFFFFF',

  primaryText: '#333333',
  secondaryText: '#666666',
  tertiaryText: '#999999',

  primaryBorder: '#E5E5E5',
  secondaryBorder: '#D0D0D0',
  activeBorder: '#1060E0',

  primaryButton: '#1060E0',
  primaryButtonText: '#FFFFFF',
  secondaryButton: '#FFFFFF',
  secondaryButtonText: '#000000',
  disabledButton: '#A0A0A0',
  disabledButtonText: '#FFFFFF',

  inputBackground: '#F3F3F3',
  inputBorder: '#DADADA',
  inputText: '#333333',
  inputPlaceholder: '#999999',

  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',

  campaignCardBackground: '#FFFFFF',
  campaignCardBorder: '#E5E5E5',
  campaignActiveBackground: '#1060E0',
  campaignInactiveBackground: '#E0E0E0',

  bannerBackground: '#FFFFFF',
  bannerBorder: '#E5E5E5',

  toggleActive: '#1060E0',
  toggleInactive: '#CCC',
  toggleText: '#555',

  iconPrimary: '#333333',
  iconSecondary: '#666666',
  iconActive: '#1060E0',

  loadingBackground: '#FFFFFF',
  loadingText: '#666666',

  emptyStateBackground: '#F5F5F5',
  emptyStateText: '#999999',
  emptyStateBorder: '#E5E5E5',

  notificationLive: '#4CAF50',
  notificationScheduled: '#FF9800',
  notificationDraft: '#999999',
  notificationPending: '#1060E0',

  historyCardBackground: '#FFFFFF',
  historyCardBorder: '#E5E5E5',
  historyStatusSuccess: '#4CAF50',
  historyStatusFailed: '#F44336',
  historyStatusPending: '#FF9800',

  playgroundBackground: '#FFFFFF',
  playgroundSidebar: '#F5F5F5',
  playgroundPreview: '#FFFFFF',
  playgroundBorder: '#E5E5E5',

  formBackground: '#FFFFFF',
  formFieldBackground: '#F3F3F3',
  formFieldBorder: '#DADADA',
  formFieldText: '#333333',
  formLabel: '#666666',
  formError: '#F44336',
  formSuccess: '#4CAF50',

  dropdownBackground: '#FFFFFF',
  dropdownBorder: '#DADADA',
  dropdownText: '#333333',
  dropdownHover: '#F5F5F5',
  dropdownSelected: '#1060E0',

  tooltipBackground: '#FFFFFF',
  tooltipText: '#333333',
  tooltipBorder: '#E5E5E5',

  scrollbarTrack: '#F5F5F5',
  scrollbarThumb: '#CCCCCC',
  scrollbarThumbHover: '#999999',
};

// Export current theme (can be switched based on user preference)
export const currentTheme = darkTheme;
