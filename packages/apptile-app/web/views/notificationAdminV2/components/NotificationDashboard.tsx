import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, ScrollView, Pressable, Image} from 'react-native';
import {useNavigate} from 'react-router';
import {useDispatch} from 'react-redux';
import {MaterialCommunityIcons} from 'apptile-core';
import {isEmpty} from 'lodash';
import moment from 'moment';

import {useNotificationContext} from '../context';
import {currentTheme} from '../styles';
import {makeToast} from '@/root/web/actions/toastActions';
import {PushNotificationApi} from '@/root/web/api/PushNotificationApi';
import {OneSignalPushNotificationApi} from '../../../api/OneSignalPushNotificationApi';

// Import types from the original notification admin
interface IManualNotification {
  id: string;
  title: string;
  body: string;
  imageUrl?: string;
  status: string;
  deliveryFrequency?: string;
  scheduledAt?: string;
  triggeredAt?: string;
}

interface IAutomatedNotification {
  id: string;
  title: string;
  body: string;
  imageUrl?: string;
  disabled: boolean;
  metadata?: {
    title: string;
    description: string;
  };
}

interface IPaginatedResponse {
  items: IManualNotification[];
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: currentTheme.background,
    flex: 1,
    minHeight: '100vh',
  },
  scrollContainer: {
    flexGrow: 1,
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  contentContainer: {
    maxWidth: 1230,
    width: '95%',
    minWidth: 768,
    marginBottom: 50,
  },
  // Header styles
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 40,
  },
  headerContent: {
    flex: 1,
  },
  mainTitle: {
    fontSize: 32,
    fontWeight: '600',
    color: currentTheme.primaryText,
    marginBottom: 8,
    lineHeight: 40,
  },
  subtitle: {
    fontSize: 16,
    color: currentTheme.secondaryText,
    lineHeight: 24,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: currentTheme.primaryButton,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginLeft: 20,
  },
  createButtonText: {
    color: currentTheme.primaryButtonText,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: currentTheme.primaryBorder,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 12,
  },
  testButtonText: {
    color: currentTheme.primaryText,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  // Section styles
  sectionContainer: {
    backgroundColor: currentTheme.cardBackground,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: currentTheme.primaryBorder,
    marginBottom: 40,
    padding: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 16,
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: currentTheme.primaryBorder,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: currentTheme.primaryText,
  },
  seeAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  seeAllText: {
    color: currentTheme.secondaryText,
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  // Campaign card styles
  campaignGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 20,
  },
  campaignCard: {
    backgroundColor: currentTheme.cardBackground,
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
    borderColor: currentTheme.primaryBorder,
    width: 320,
    minHeight: 180,
  },
  campaignCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  campaignTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: currentTheme.primaryText,
    flex: 1,
    marginRight: 10,
  },
  campaignStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontSize: 12,
    fontWeight: '500',
  },
  statusLive: {
    backgroundColor: currentTheme.success,
    color: '#FFFFFF',
  },
  statusScheduled: {
    backgroundColor: currentTheme.warning,
    color: '#FFFFFF',
  },
  statusDraft: {
    backgroundColor: currentTheme.secondaryText,
    color: '#FFFFFF',
  },
  campaignDescription: {
    fontSize: 14,
    color: currentTheme.secondaryText,
    lineHeight: 20,
    marginBottom: 16,
  },
  campaignActions: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 'auto',
  },
  campaignActionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: currentTheme.primaryBorder,
    backgroundColor: currentTheme.secondaryCardBackground,
  },
  campaignActionText: {
    fontSize: 12,
    color: currentTheme.primaryText,
    fontWeight: '500',
  },
  // Empty state styles
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
    paddingVertical: 40,
  },
  emptyStateIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: currentTheme.secondaryCardBackground,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  emptyStateText: {
    fontSize: 16,
    color: currentTheme.primaryText,
    textAlign: 'center',
    marginBottom: 20,
    fontWeight: '500',
  },
  emptyStateButton: {
    backgroundColor: currentTheme.primaryButton,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: currentTheme.primaryButtonText,
    fontSize: 14,
    fontWeight: '600',
  },
  loadingContainer: {
    backgroundColor: currentTheme.loadingBackground,
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
  },
  loadingText: {
    fontSize: 14,
    color: currentTheme.emptyStateText,
    textAlign: 'center',
  },
});

const NotificationDashboard: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {appId, activeNotificationProvider, hasOneSignal, oneSignalAppId} = useNotificationContext();

  const [manualList, setManualList] = useState<IManualNotification[]>([]);
  const [automatedList, setAutomatedList] = useState<IAutomatedNotification[]>([]);
  const [exploreList, setExploreList] = useState<IAutomatedNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedNotify, setSelectedNotify] = useState<'PUSH' | 'IN_APP'>('PUSH');

  // Check if notification provider is supported
  useEffect(() => {
    if (activeNotificationProvider !== 'oneSignal' && activeNotificationProvider !== 'APPTILE') {
      navigate(`../notifications/unavailable/${activeNotificationProvider}`);
    }
  }, [activeNotificationProvider, navigate]);

  // Load manual notifications
  useEffect(() => {
    if (!appId) return;

    const loadManualNotifications = async () => {
      try {
        if (!hasOneSignal) {
          const response = await PushNotificationApi.listManualRecord<IPaginatedResponse>(
            appId,
            3,
            0,
            'PENDING,DRAFT,LIVE',
          );

          if (response.status >= 200 && response.status <= 299) {
            setManualList(response.data.items);
          }
        } else {
          const notificationData = await OneSignalPushNotificationApi.getNotifications(oneSignalAppId, appId, 10, 0);
          const requiredNotifications = notificationData?.data?.notifications?.filter(
            n => !n.completed_at && !n.canceled,
          );

          if (notificationData.status >= 200 && notificationData.status <= 299) {
            // Transform OneSignal data to match our interface
            const transformedNotifications = requiredNotifications.map(n => ({
              id: n.id,
              title: n.headings?.en || '',
              body: n.contents?.en || '',
              imageUrl: n.big_picture || '',
              status: n.send_after ? 'PENDING' : 'DRAFT',
            }));
            setManualList(transformedNotifications);
          }
        }
      } catch (error) {
        console.error('Error loading manual notifications:', error);
      } finally {
        setLoading(false);
      }
    };

    loadManualNotifications();
  }, [appId, hasOneSignal, oneSignalAppId]);

  // Load automated notifications
  useEffect(() => {
    if (!appId || hasOneSignal) return;

    const loadAutomatedNotifications = async () => {
      try {
        const response = await PushNotificationApi.listAutomatedRecord<IAutomatedNotification[]>(appId);

        if (response.status >= 200 && response.status <= 299) {
          const activeAutomated = response.data.filter(e => !e.disabled);
          const inactiveAutomated = response.data.filter(e => e.disabled);

          setAutomatedList(activeAutomated);
          setExploreList(inactiveAutomated);
        }
      } catch (error) {
        console.error('Error loading automated notifications:', error);
      }
    };

    loadAutomatedNotifications();
  }, [appId, hasOneSignal]);

  const handleEditCampaign = (id: string) => {
    navigate(`../notifications/manual?action=edit&notificationId=${id}`);
  };

  const handleDeleteCampaign = async (id: string, status?: string) => {
    try {
      if (!hasOneSignal) {
        await PushNotificationApi.deleteManualRecord(id, appId);
      } else {
        if (status === 'DRAFT') {
          await OneSignalPushNotificationApi.deleteTemplate(oneSignalAppId, appId, id);
        } else if (status === 'PENDING') {
          await OneSignalPushNotificationApi.stopPushNotification(appId, id, oneSignalAppId);
        }
      }
      setManualList(prev => prev.filter(entry => entry.id !== id));
    } catch (error) {
      dispatch(
        makeToast({
          content: 'Unable to delete notification',
          appearances: 'error',
        }),
      );
    }
  };

  const handleStopCampaign = async (id: string) => {
    try {
      await PushNotificationApi.stopLiveRecurringCampaign(id, appId);
      setManualList(prev => prev.filter(entry => entry.id !== id));
    } catch (error) {
      dispatch(
        makeToast({
          content: 'Unable to stop campaign',
          appearances: 'error',
        }),
      );
    }
  };

  const handlePauseAutomated = async (id: string) => {
    try {
      await PushNotificationApi.updateAutomatedRecord(id, appId, {disabled: true});
      setAutomatedList(prev => prev.filter(entry => entry.id !== id));
    } catch (error) {
      console.error('Error pausing automated notification:', error);
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'LIVE':
        return 'LIVE';
      case 'PENDING':
        return 'SCHEDULED';
      default:
        return 'DRAFT';
    }
  };

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'LIVE':
        return styles.statusLive;
      case 'PENDING':
        return styles.statusScheduled;
      default:
        return styles.statusDraft;
    }
  };

  const getDescriptionText = (entry: IManualNotification) => {
    let descriptionText = '';
    if (entry.deliveryFrequency === 'RECURRING') {
      descriptionText = 'Recurring Campaign - ';
      if (entry.status === 'LIVE') {
        return `${descriptionText}Started at ${moment(entry.triggeredAt).format('Do MMM, LT')}`;
      }
    }
    if (entry.scheduledAt) {
      return `${descriptionText}Scheduled for ${moment(entry.scheduledAt).format('Do MMM [at] LT')}`;
    }
    return '';
  };

  const renderCampaignCard = (campaign: IManualNotification) => (
    <View key={campaign.id} style={styles.campaignCard}>
      <View style={styles.campaignCardHeader}>
        <Text style={styles.campaignTitle} numberOfLines={2}>
          {campaign.title || 'Untitled Campaign'}
        </Text>
        <Text style={[styles.campaignStatus, getStatusStyle(campaign.status)]}>{getStatusText(campaign.status)}</Text>
      </View>
      <Text style={styles.campaignDescription} numberOfLines={3}>
        {getDescriptionText(campaign)}
      </Text>
      <View style={styles.campaignActions}>
        <Pressable style={styles.campaignActionButton} onPress={() => handleEditCampaign(campaign.id)}>
          <Text style={styles.campaignActionText}>Edit</Text>
        </Pressable>
        {campaign.status !== 'LIVE' && (
          <Pressable
            style={styles.campaignActionButton}
            onPress={() => handleDeleteCampaign(campaign.id, campaign.status)}>
            <Text style={styles.campaignActionText}>Delete</Text>
          </Pressable>
        )}
        {campaign.status === 'LIVE' && (
          <Pressable style={styles.campaignActionButton} onPress={() => handleStopCampaign(campaign.id)}>
            <Text style={styles.campaignActionText}>Stop</Text>
          </Pressable>
        )}
      </View>
    </View>
  );

  const renderAutomatedCard = (campaign: IAutomatedNotification) => (
    <View key={campaign.id} style={styles.campaignCard}>
      <View style={styles.campaignCardHeader}>
        <Text style={styles.campaignTitle} numberOfLines={2}>
          {campaign.metadata?.title || campaign.title || 'Untitled Campaign'}
        </Text>
        <Text style={[styles.campaignStatus, styles.statusLive]}>LIVE</Text>
      </View>
      <Text style={styles.campaignDescription} numberOfLines={3}>
        {campaign.metadata?.description || ''}
      </Text>
      <View style={styles.campaignActions}>
        <Pressable
          style={styles.campaignActionButton}
          onPress={() => navigate(`../notifications/automated?notificationId=${campaign.id}`)}>
          <Text style={styles.campaignActionText}>Edit</Text>
        </Pressable>
        <Pressable style={styles.campaignActionButton} onPress={() => handlePauseAutomated(campaign.id)}>
          <Text style={styles.campaignActionText}>Pause</Text>
        </Pressable>
      </View>
    </View>
  );

  // Separate campaigns by status
  const activeCampaigns = [...manualList.filter(campaign => campaign.status === 'LIVE'), ...automatedList];
  const scheduledCampaigns = manualList.filter(campaign => campaign.status === 'PENDING');
  const historyCampaigns = manualList.filter(campaign => campaign.status === 'DRAFT');

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.wrapper}>
          <View style={styles.contentContainer}>
            {/* Header */}
            <View style={styles.headerContainer}>
              <View style={styles.headerContent}>
                <Text style={styles.mainTitle}>Push Notifications</Text>
                <Text style={styles.subtitle}>Boost customer retention with Notifications</Text>
              </View>
              <View style={{flexDirection: 'row', alignItems: 'center'}}>
                <Pressable style={styles.testButton} onPress={() => navigate('../notifications/manual?action=test')}>
                  <MaterialCommunityIcons name="send" size={16} color={currentTheme.primaryText} />
                  <Text style={styles.testButtonText}>Send Test Notification</Text>
                </Pressable>
                <Pressable style={styles.createButton} onPress={() => navigate('../notifications/manual')}>
                  <MaterialCommunityIcons name="plus" size={16} color={currentTheme.primaryButtonText} />
                  <Text style={styles.createButtonText}>Create New</Text>
                </Pressable>
              </View>
            </View>

            {/* Active Campaigns Section */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Active Campaigns</Text>
                <Pressable style={styles.seeAllButton} onPress={() => navigate('../notifications/activeCampaign')}>
                  <Text style={styles.seeAllText}>See all</Text>
                  <MaterialCommunityIcons name="chevron-right" size={16} color={currentTheme.secondaryText} />
                </Pressable>
              </View>
              <View style={styles.campaignGrid}>
                {loading ? (
                  <View style={styles.loadingContainer}>
                    <Text style={styles.emptyStateText}>Loading campaigns...</Text>
                  </View>
                ) : activeCampaigns.length > 0 ? (
                  activeCampaigns
                    .slice(0, 3)
                    .map(campaign =>
                      'metadata' in campaign ? renderAutomatedCard(campaign) : renderCampaignCard(campaign),
                    )
                ) : (
                  <View style={styles.emptyState}>
                    <Text style={styles.emptyStateText}>No Active Campaigns</Text>
                  </View>
                )}
              </View>
            </View>

            {/* Scheduled Section */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Scheduled</Text>
                <Pressable style={styles.seeAllButton} onPress={() => navigate('../notifications/activeCampaign')}>
                  <Text style={styles.seeAllText}>See all</Text>
                  <MaterialCommunityIcons name="chevron-right" size={16} color={currentTheme.secondaryText} />
                </Pressable>
              </View>
              <View style={styles.campaignGrid}>
                {scheduledCampaigns.length > 0 ? (
                  scheduledCampaigns.slice(0, 3).map(renderCampaignCard)
                ) : (
                  <View style={styles.emptyState}>
                    <Text style={styles.emptyStateText}>No Scheduled Campaigns</Text>
                  </View>
                )}
              </View>
            </View>

            {/* History Section */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>History</Text>
                <Pressable style={styles.seeAllButton} onPress={() => navigate('../notifications/history')}>
                  <Text style={styles.seeAllText}>See all</Text>
                  <MaterialCommunityIcons name="chevron-right" size={16} color={currentTheme.secondaryText} />
                </Pressable>
              </View>
              <View style={styles.campaignGrid}>
                {historyCampaigns.length > 0 ? (
                  historyCampaigns.slice(0, 3).map(renderCampaignCard)
                ) : (
                  <View style={styles.emptyState}>
                    <Text style={styles.emptyStateText}>No Campaign History</Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default NotificationDashboard;
