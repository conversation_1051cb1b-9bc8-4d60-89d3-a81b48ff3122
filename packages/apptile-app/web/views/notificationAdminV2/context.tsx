import React, {createContext, useContext, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import {useParams} from '@/root/web/routing.web';

interface NotificationContextType {
  appId: string;
  activeNotificationProvider: string | null;
  hasOneSignal: boolean;
  oneSignalAppId: string | null;
  loading: boolean;
  setLoading: (loading: boolean) => void;
}

// Create the context
export const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Provide context to the app
export const NotificationProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
  const param = useParams();
  const [loading, setLoading] = useState(true);

  const appId = useSelector((state: EditorRootState) => state.apptile.appId) || (param as any).id;
  const platformState = useSelector((state: EditorRootState) => state.platform);
  const {activeNotificationProvider, hasOneSignal, oneSignalAppId} = platformState;

  useEffect(() => {
    // Initialize any required data here
    setLoading(false);
  }, [appId]);

  return (
    <NotificationContext.Provider
      value={{
        appId,
        activeNotificationProvider,
        hasOneSignal,
        oneSignalAppId,
        loading,
        setLoading,
      }}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook to use the context
export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
};
