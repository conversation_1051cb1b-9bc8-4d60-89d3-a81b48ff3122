import React from 'react';
import {Routes, Route} from 'react-router-dom';
import NotificationDashboard from './components/NotificationDashboard';
import {NotificationProvider} from './context';

export const NotificationRouterV2: React.FC = () => {
  return (
    <NotificationProvider>
      <Routes>
        <Route path="/" element={<NotificationDashboard />} />
      </Routes>
    </NotificationProvider>
  );
};
