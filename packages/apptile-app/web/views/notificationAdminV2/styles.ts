// Dark theme styles for Notification Admin V2
export const darkTheme = {
  // Main backgrounds
  background: '#000000',
  headerBackground: '#1A1D20',
  cardBackground: '#1A1D20',
  secondaryCardBackground: '#292B32',
  modalBackground: '#2A2A2A',

  // Text colors
  primaryText: '#FFFFFF',
  secondaryText: '#B0B0B0',
  tertiaryText: '#808080',

  // Border colors
  primaryBorder: '#333333',
  secondaryBorder: '#2A2A2A',
  activeBorder: '#1060E0',
  whiteBorder: 'white',

  // Button colors
  primaryButton: '#1060E0',
  primaryButtonText: '#FFFFFF',
  secondaryButton: '#333333',
  secondaryButtonText: '#FFFFFF',
  disabledButton: '#404040',
  disabledButtonText: '#666666',

  // Input colors
  inputBackground: '#292B32',
  inputBorder: '#404040',
  inputText: '#FFFFFF',
  inputPlaceholder: '#808080',

  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',

  // Campaign card colors
  campaignCardBackground: '#1A1A1A',
  campaignCardBorder: '#333333',
  campaignActiveBackground: '#1060E0',
  campaignInactiveBackground: '#404040',

  // Banner colors
  bannerBackground: '#1A1D20',
  bannerBorder: '#333333',

  // Toggle colors
  toggleActive: '#1060E0',
  toggleInactive: '#404040',
  toggleText: '#FFFFFF',

  // Icon colors
  iconPrimary: '#FFFFFF',
  iconSecondary: '#B0B0B0',
  iconActive: '#1060E0',

  // Loading colors
  loadingBackground: '#1A1D20',
  loadingText: '#B0B0B0',

  // Empty state colors
  emptyStateBackground: '#2A2A2A',
  emptyStateText: '#808080',
  emptyStateBorder: '#404040',
};

// Export current theme
export const currentTheme = darkTheme;
