import { launchBrowser } from "../../firebase/puppeteer/launchBrowser.js";
// import fs from "fs";
import { login } from "./login.js";
import config from "../../../config/index.js";
import { delay } from "../../index.js";

try {
  // !!! For now this works only for Apptile Appstore account
  // We need myacinfo value which is sent as http only cookie and expiry type with session.

  // We need csrf, csrf_ts token values which are fetched through local storage

  var [browser, page] = await launchBrowser();

  const client = await page.target().createCDPSession();
  const { username, password, teamId } = config.appstore.credentials;
  await login(page, username, password);

  await delay(5000);
  await page.goto("https://developer.apple.com/account");
  await delay(10000);

  await page.evaluate((teamId) => {
    document
      .querySelector(
        "#__next > div > div > nav > div > nav > div.UserMenuToggle_UserMenuToggle__hngV8 > span"
      )
      .click();
    document.querySelector(`li[data-id="${teamId}"]`).click();
    console.log(document.querySelector(`li[data-id="${teamId}"]`));
  }, teamId);

  await delay(10000);
  const cookies = (await client.send("Network.getAllCookies")).cookies;

  const filteredCookies = cookies.filter(
    (cookie) => cookie.name === "myacinfo"
  );
  const storedTeamId = await getLocalStorageValue(page, "adp.session.teamId");
  if (storedTeamId !== teamId) {
    throw `${teamId} 's Team Profile is not selected on https://developer.apple.com/account. Crashing for safety as changes are dangerous and sometimes irreversible!`;
  }
  const myacinfo = filteredCookies[0].value;
  const csrf = await getLocalStorageValue(page, "csrf");
  const csrf_ts = await getLocalStorageValue(page, "csrf_ts");

  var session = { myacinfo, csrf, csrf_ts };
} catch (error) {
  throw new Error("Error while creating Appstore Session" + error);
} finally {
  browser.close();
}

async function getLocalStorageValue(page, key) {
  const localStorageValue = await page.evaluate((key) => {
    return localStorage.getItem(key);
  }, key);

  return localStorageValue;
}

export default session;
